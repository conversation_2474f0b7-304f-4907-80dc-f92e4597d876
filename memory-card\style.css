:root {
    --primary-color: #3498db;
    --secondary-color: #2ecc71;
    --accent-color: #e67e22;
    --dark-color: #2c3e50;
    --light-color: #ecf0f1;
    --card-color: #1abc9c;
    --card-back: #9b59b6;
    --transition: all 0.3s ease;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f5f7fa;
    color: var(--dark-color);
    line-height: 1.6;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

.container {
    max-width: 900px;
    margin: 0 auto;
    padding: 20px;
    flex-grow: 1;
}

header {
    text-align: center;
    margin-bottom: 30px;
    background-color: white;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

h1 {
    font-size: 2.2rem;
    color: var(--dark-color);
    margin-bottom: 20px;
}

.game-info {
    display: flex;
    justify-content: center;
    margin-bottom: 15px;
    font-size: 1.2rem;
}

.score, .time {
    margin: 0 15px;
    padding: 5px 10px;
    background-color: var(--light-color);
    border-radius: 5px;
}

.controls {
    margin-bottom: 15px;
}

.btn {
    background-color: var(--primary-color);
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 5px;
    cursor: pointer;
    font-size: 1rem;
    margin: 0 5px;
    transition: var(--transition);
}

.btn:hover {
    background-color: #2980b9;
    transform: translateY(-2px);
}

.difficulty {
    margin-top: 10px;
}

.difficulty-btn {
    background-color: var(--light-color);
    color: var(--dark-color);
    border: none;
    padding: 5px 10px;
    border-radius: 5px;
    cursor: pointer;
    font-size: 0.9rem;
    margin: 0 5px;
    transition: var(--transition);
}

.difficulty-btn.active {
    background-color: var(--accent-color);
    color: white;
}

.game-container {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 15px;
    perspective: 1000px;
    margin-bottom: 30px;
}

.card {
    aspect-ratio: 3/4;
    position: relative;
    transform-style: preserve-3d;
    transition: transform 0.6s;
    cursor: pointer;
}

.card.flipped {
    transform: rotateY(180deg);
}

.card-front, .card-back {
    position: absolute;
    width: 100%;
    height: 100%;
    backface-visibility: hidden;
    border-radius: 8px;
    display: flex;
    justify-content: center;
    align-items: center;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.card-front {
    background-color: var(--card-color);
    transform: rotateY(180deg);
    color: white;
    font-size: 2rem;
}

.card-back {
    background-color: var(--card-back);
    background-image: linear-gradient(135deg, var(--card-back), #8e44ad);
}

.card-back::before {
    content: '?';
    font-size: 2.5rem;
    color: rgba(255, 255, 255, 0.5);
}

.overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 100;
    opacity: 1;
    transition: opacity 0.3s ease;
}

.overlay.hidden {
    opacity: 0;
    pointer-events: none;
}

.popup {
    background-color: white;
    border-radius: 10px;
    padding: 30px;
    text-align: center;
    max-width: 400px;
    transform: scale(1);
    transition: transform 0.3s ease;
}

.hidden .popup {
    transform: scale(0.8);
}

.popup h2 {
    margin-bottom: 15px;
    color: var(--primary-color);
}

.popup p {
    margin-bottom: 10px;
    font-size: 1.1rem;
}

#play-again {
    margin-top: 20px;
}

footer {
    text-align: center;
    padding: 20px;
    background-color: var(--light-color);
    margin-top: auto;
}

.back-link {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: bold;
    transition: var(--transition);
}

.back-link:hover {
    color: var(--secondary-color);
}

/* Responsive Grid Adjustments */
@media (max-width: 768px) {
    .game-container {
        gap: 10px;
    }
    
    h1 {
        font-size: 1.8rem;
    }
    
    .game-info {
        flex-direction: column;
        align-items: center;
    }
    
    .score, .time {
        margin: 5px 0;
    }
}

@media (max-width: 480px) {
    .game-container {
        gap: 8px;
    }
    
    .card-front {
        font-size: 1.5rem;
    }
    
    .controls, .difficulty {
        display: flex;
        flex-wrap: wrap;
        justify-content: center;
    }
    
    .btn, .difficulty-btn {
        margin: 5px;
    }
} 