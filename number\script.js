document.addEventListener('DOMContentLoaded', () => {
    // 游戏元素
    const guessInput = document.getElementById('guess-input');
    const guessBtn = document.getElementById('guess-btn');
    const message = document.getElementById('message');
    const attemptsElement = document.getElementById('attempts');
    const historyList = document.getElementById('history-list');
    const newGameBtn = document.getElementById('new-game-btn');
    const hintBtn = document.getElementById('hint-btn');
    const progressBar = document.getElementById('progress-bar');
    const gamesPlayed = document.getElementById('games-played');
    const bestScore = document.getElementById('best-score');
    
    // 模态框元素
    const modal = document.getElementById('modal');
    const modalTitle = document.getElementById('modal-title');
    const modalMessage = document.getElementById('modal-message');
    const modalClose = document.getElementById('modal-close');
    
    // 游戏变量
    let secretNumber;
    let attempts;
    let maxAttempts = 10;
    let gameOver = false;
    let gamesCounter = 0;
    let bestAttempts = Infinity;
    let guessHistory = [];
    
    // 初始化游戏
    initGame();
    loadStats();
    
    // 按钮事件监听器
    guessBtn.addEventListener('click', handleGuess);
    guessInput.addEventListener('keypress', (e) => {
        if (e.key === 'Enter') handleGuess();
    });
    newGameBtn.addEventListener('click', initGame);
    hintBtn.addEventListener('click', giveHint);
    modalClose.addEventListener('click', closeModal);
    
    // 初始化游戏
    function initGame() {
        secretNumber = Math.floor(Math.random() * 100) + 1;
        attempts = maxAttempts;
        gameOver = false;
        guessHistory = [];
        
        attemptsElement.textContent = attempts;
        message.textContent = '准备好了吗？开始猜数字吧！';
        message.className = '';
        historyList.innerHTML = '';
        guessInput.value = '';
        guessInput.disabled = false;
        guessBtn.disabled = false;
        newGameBtn.classList.add('hidden');
        progressBar.style.width = '100%';
        
        guessInput.focus();
    }
    
    // 处理猜测
    function handleGuess() {
        if (gameOver) return;
        
        const userGuess = parseInt(guessInput.value);
        
        // 验证输入
        if (isNaN(userGuess) || userGuess < 1 || userGuess > 100) {
            showMessage('请输入1到100之间的有效数字！', 'error');
            return;
        }
        
        // 检查是否已经猜过这个数字
        if (guessHistory.includes(userGuess)) {
            showMessage('你已经猜过这个数字了，请尝试其他数字！', 'warning');
            return;
        }
        
        attempts--;
        attemptsElement.textContent = attempts;
        updateProgressBar();
        
        // 记录猜测历史
        guessHistory.push(userGuess);
        addToHistory(userGuess);
        
        // 检查猜测结果
        if (userGuess === secretNumber) {
            // 猜对了
            gameWon();
        } else if (attempts === 0) {
            // 用尽所有尝试次数
            gameLost();
        } else {
            // 猜错了，但还有机会
            const hint = userGuess > secretNumber ? '太大了' : '太小了';
            showMessage(`${userGuess} ${hint}，再试一次！`, userGuess > secretNumber ? 'error' : 'warning');
        }
        
        guessInput.value = '';
        guessInput.focus();
    }
    
    // 游戏胜利
    function gameWon() {
        const usedAttempts = maxAttempts - attempts;
        gameOver = true;
        showMessage(`恭喜你！你用了 ${usedAttempts} 次尝试猜对了数字 ${secretNumber}！`, 'success');
        endGame();
        
        // 更新统计数据
        gamesCounter++;
        if (usedAttempts < bestAttempts || bestAttempts === Infinity) {
            bestAttempts = usedAttempts;
        }
        updateStats();
    }
    
    // 游戏失败
    function gameLost() {
        gameOver = true;
        showMessage(`游戏结束！正确的数字是 ${secretNumber}。`, 'error');
        endGame();
        
        // 更新统计数据
        gamesCounter++;
        updateStats();
    }
    
    // 结束游戏
    function endGame() {
        guessInput.disabled = true;
        guessBtn.disabled = true;
        newGameBtn.classList.remove('hidden');
    }
    
    // 提供提示
    function giveHint() {
        if (gameOver || guessHistory.length === 0) {
            showModal('游戏提示', '先猜一个数字吧！我会给你一些反馈。');
            return;
        }
        
        // 确定一个有用的提示
        let hintText = '';
        const closestGuess = findClosestGuess();
        
        if (Math.abs(secretNumber - closestGuess) <= 5) {
            hintText = `你非常接近了！正确的数字在 ${closestGuess} 附近。`;
        } else if (Math.abs(secretNumber - closestGuess) <= 15) {
            hintText = `你正在接近！正确的数字与 ${closestGuess} 相差不远。`;
        } else {
            // 提供一个范围
            const min = Math.max(1, secretNumber - 20);
            const max = Math.min(100, secretNumber + 20);
            hintText = `提示：正确的数字在 ${min} 到 ${max} 之间。`;
        }
        
        showModal('游戏提示', hintText);
    }
    
    // 找到最接近的猜测
    function findClosestGuess() {
        let closest = guessHistory[0];
        let minDiff = Math.abs(secretNumber - closest);
        
        for (let i = 1; i < guessHistory.length; i++) {
            const diff = Math.abs(secretNumber - guessHistory[i]);
            if (diff < minDiff) {
                minDiff = diff;
                closest = guessHistory[i];
            }
        }
        
        return closest;
    }
    
    // 添加猜测到历史记录
    function addToHistory(guess) {
        const li = document.createElement('li');
        const guessSpan = document.createElement('span');
        const resultSpan = document.createElement('span');
        
        guessSpan.textContent = `猜测: ${guess}`;
        
        if (guess === secretNumber) {
            resultSpan.textContent = '正确！';
            resultSpan.className = 'correct';
        } else if (guess > secretNumber) {
            resultSpan.textContent = '太大了';
            resultSpan.className = 'too-high';
        } else {
            resultSpan.textContent = '太小了';
            resultSpan.className = 'too-low';
        }
        
        li.appendChild(guessSpan);
        li.appendChild(resultSpan);
        historyList.prepend(li);
    }
    
    // 显示消息
    function showMessage(text, type) {
        message.textContent = text;
        message.className = '';
        
        if (type === 'success') {
            message.classList.add('message-success');
        } else if (type === 'warning') {
            message.classList.add('message-warning');
        } else if (type === 'error') {
            message.classList.add('message-error');
        }
    }
    
    // 更新进度条
    function updateProgressBar() {
        const percentage = (attempts / maxAttempts) * 100;
        progressBar.style.width = `${percentage}%`;
    }
    
    // 显示模态框
    function showModal(title, message) {
        modalTitle.textContent = title;
        modalMessage.textContent = message;
        modal.style.display = 'flex';
    }
    
    // 关闭模态框
    function closeModal() {
        modal.style.display = 'none';
    }
    
    // 更新统计数据
    function updateStats() {
        gamesPlayed.textContent = gamesCounter;
        bestScore.textContent = bestAttempts === Infinity ? '-' : bestAttempts;
        saveStats();
    }
    
    // 保存统计数据到本地存储
    function saveStats() {
        localStorage.setItem('numberGameStats', JSON.stringify({
            gamesPlayed: gamesCounter,
            bestScore: bestAttempts
        }));
    }
    
    // 从本地存储加载统计数据
    function loadStats() {
        const stats = JSON.parse(localStorage.getItem('numberGameStats'));
        if (stats) {
            gamesCounter = stats.gamesPlayed || 0;
            bestAttempts = stats.bestScore || Infinity;
            updateStats();
        }
    }
    
    // 关闭模态框点击外部
    window.addEventListener('click', (e) => {
        if (e.target === modal) {
            closeModal();
        }
    });
});