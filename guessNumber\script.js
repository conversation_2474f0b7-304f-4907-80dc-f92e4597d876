// 游戏主要变量
let targetNumber;
let guessHistory = [];
let gameStarted = false;
let gameOver = false;
let startTime;
let timerInterval;

// DOM元素
const guessInput = document.getElementById('guess-input');
const guessButton = document.getElementById('guess-button');
const resetButton = document.getElementById('reset-button');
const message = document.getElementById('message');
const guessHistoryList = document.getElementById('guess-history');
const historyTitle = document.getElementById('history-title');
const statsArea = document.getElementById('stats-area');
const guessCount = document.getElementById('guess-count');
const timeUsed = document.getElementById('time-used');

// 初始化游戏
function initGame() {
    targetNumber = Math.floor(Math.random() * 100) + 1;
    guessHistory = [];
    gameStarted = false;
    gameOver = false;
    
    // 重置UI
    message.textContent = '请输入1到100之间的数字';
    message.className = '';
    guessHistoryList.innerHTML = '';
    historyTitle.classList.add('hidden');
    statsArea.classList.add('hidden');
    guessInput.value = '';
    guessInput.focus();
    
    // 停止计时器
    if (timerInterval) {
        clearInterval(timerInterval);
        timerInterval = null;
    }
    
    console.log('游戏已初始化，目标数字是：', targetNumber);
}

// 开始计时
function startTimer() {
    startTime = new Date();
    timerInterval = setInterval(updateTimer, 1000);
}

// 更新计时器
function updateTimer() {
    if (!gameStarted || gameOver) return;
    
    const currentTime = new Date();
    const elapsedSeconds = Math.floor((currentTime - startTime) / 1000);
    timeUsed.textContent = elapsedSeconds;
}

// 处理猜测
function handleGuess() {
    // 获取并验证输入
    const userGuess = parseInt(guessInput.value);
    
    if (isNaN(userGuess) || userGuess < 1 || userGuess > 100) {
        message.textContent = '请输入1到100之间的有效数字！';
        message.className = 'too-high';
        guessInput.value = '';
        guessInput.focus();
        return;
    }
    
    // 如果是第一次猜测，开始游戏计时
    if (!gameStarted) {
        gameStarted = true;
        startTimer();
        historyTitle.classList.remove('hidden');
        statsArea.classList.remove('hidden');
    }
    
    // 记录猜测历史
    guessHistory.push(userGuess);
    guessCount.textContent = guessHistory.length;
    
    // 创建历史记录项
    const historyItem = document.createElement('li');
    historyItem.textContent = userGuess;
    
    // 检查猜测结果
    if (userGuess === targetNumber) {
        // 猜对了
        message.textContent = `恭喜你！${userGuess}是正确的数字！`;
        message.className = 'correct';
        gameOver = true;
        clearInterval(timerInterval);
        
        // 禁用输入和猜测按钮
        guessInput.disabled = true;
        guessButton.disabled = true;
        
        // 显示庆祝动画
        celebrate();
        
        // 高亮正确的猜测
        historyItem.style.backgroundColor = '#27ae60';
        historyItem.style.color = 'white';
        historyItem.style.border = 'none';
    } else if (userGuess > targetNumber) {
        // 猜大了
        message.textContent = `${userGuess}太大了，再试一次！`;
        message.className = 'too-high';
        historyItem.classList.add('too-high-item');
    } else {
        // 猜小了
        message.textContent = `${userGuess}太小了，再试一次！`;
        message.className = 'too-low';
        historyItem.classList.add('too-low-item');
    }
    
    // 添加到历史记录列表
    guessHistoryList.appendChild(historyItem);
    
    // 清空输入框并聚焦
    guessInput.value = '';
    guessInput.focus();
}

// 键盘事件处理（按Enter键提交）
function handleKeyPress(e) {
    if (e.key === 'Enter' && !gameOver) {
        handleGuess();
    }
}

// 添加事件监听器
guessButton.addEventListener('click', handleGuess);
resetButton.addEventListener('click', initGame);
guessInput.addEventListener('keypress', handleKeyPress);

// 初始化游戏
initGame();

// 移动设备的用户体验优化
guessInput.addEventListener('blur', function() {
    // 在移动设备上，当输入框失去焦点后，避免键盘收起导致布局变动
    setTimeout(() => {
        window.scrollTo(0, 0);
    }, 100);
});

// 添加一点趣味：当猜对时，进行简单的庆祝动画
function celebrate() {
    const container = document.querySelector('.container');
    container.style.animation = 'pulse 0.5s 3';
    
    setTimeout(() => {
        container.style.animation = '';
    }, 1500);
}