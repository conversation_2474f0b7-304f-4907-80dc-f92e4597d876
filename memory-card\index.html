<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>记忆翻牌游戏</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="container">
        <header>
            <h1>记忆翻牌游戏</h1>
            <div class="game-info">
                <div class="score">翻牌次数: <span id="flips">0</span></div>
                <div class="time">时间: <span id="time">00:00</span></div>
            </div>
            <div class="controls">
                <button id="start-btn" class="btn">开始游戏</button>
                <button id="reset-btn" class="btn">重置游戏</button>
            </div>
            <div class="difficulty">
                <span>难度: </span>
                <button id="easy" class="difficulty-btn active" data-grid="4x3">简单</button>
                <button id="medium" class="difficulty-btn" data-grid="4x4">中等</button>
                <button id="hard" class="difficulty-btn" data-grid="5x4">困难</button>
            </div>
        </header>
        
        <div id="game-container" class="game-container">
            <!-- 卡片将通过JavaScript动态生成 -->
        </div>
        
        <div id="game-over" class="overlay hidden">
            <div class="popup">
                <h2>游戏结束!</h2>
                <p>你用了 <span id="final-flips">0</span> 次翻牌</p>
                <p>用时: <span id="final-time">00:00</span></p>
                <button id="play-again" class="btn">再玩一次</button>
            </div>
        </div>
    </div>
    
    <footer>
        <a href="../index.html" class="back-link">返回主页</a>
    </footer>
    
    <script src="script.js"></script>
</body>
</html> 