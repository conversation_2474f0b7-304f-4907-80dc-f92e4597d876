// 获取DOM元素
const cityInput = document.getElementById('city-input');
const searchBtn = document.getElementById('search-btn');
const cityName = document.getElementById('city-name');
const currentDate = document.getElementById('current-date');
const temperature = document.getElementById('temperature');
const weatherIcon = document.getElementById('weather-icon');
const humidity = document.getElementById('humidity');
const windSpeed = document.getElementById('wind-speed');
const forecastContainer = document.getElementById('forecast-container');
const cityList = document.getElementById('city-list');
const cityFilter = document.getElementById('city-filter');

// 城市天气数据（模拟数据）
const citiesWeather = {
    '北京': {
        current: {
            temp: 25,
            humidity: 45,
            windSpeed: 5.2,
            weather: 'sunny' // 晴天
        },
        forecast: [
            { day: '星期一', temp: 26, weather: 'sunny' },
            { day: '星期二', temp: 27, weather: 'partly-cloudy' },
            { day: '星期三', temp: 25, weather: 'cloudy' },
            { day: '星期四', temp: 22, weather: 'rainy' },
            { day: '星期五', temp: 24, weather: 'sunny' }
        ]
    },
    '上海': {
        current: {
            temp: 28,
            humidity: 65,
            windSpeed: 4.8,
            weather: 'partly-cloudy' // 多云
        },
        forecast: [
            { day: '星期一', temp: 29, weather: 'partly-cloudy' },
            { day: '星期二', temp: 30, weather: 'sunny' },
            { day: '星期三', temp: 28, weather: 'rainy' },
            { day: '星期四', temp: 26, weather: 'thunderstorm' },
            { day: '星期五', temp: 27, weather: 'partly-cloudy' }
        ]
    },
    '广州': {
        current: {
            temp: 32,
            humidity: 70,
            windSpeed: 3.5,
            weather: 'rainy' // 雨天
        },
        forecast: [
            { day: '星期一', temp: 31, weather: 'rainy' },
            { day: '星期二', temp: 30, weather: 'thunderstorm' },
            { day: '星期三', temp: 29, weather: 'rainy' },
            { day: '星期四', temp: 30, weather: 'partly-cloudy' },
            { day: '星期五', temp: 32, weather: 'sunny' }
        ]
    },
    '深圳': {
        current: {
            temp: 30,
            humidity: 68,
            windSpeed: 3.9,
            weather: 'partly-cloudy' // 多云
        },
        forecast: [
            { day: '星期一', temp: 31, weather: 'partly-cloudy' },
            { day: '星期二', temp: 32, weather: 'sunny' },
            { day: '星期三', temp: 30, weather: 'rainy' },
            { day: '星期四', temp: 29, weather: 'rainy' },
            { day: '星期五', temp: 30, weather: 'partly-cloudy' }
        ]
    },
    '杭州': {
        current: {
            temp: 26,
            humidity: 60,
            windSpeed: 4.2,
            weather: 'cloudy' // 阴天
        },
        forecast: [
            { day: '星期一', temp: 27, weather: 'cloudy' },
            { day: '星期二', temp: 28, weather: 'partly-cloudy' },
            { day: '星期三', temp: 29, weather: 'sunny' },
            { day: '星期四', temp: 27, weather: 'rainy' },
            { day: '星期五', temp: 25, weather: 'cloudy' }
        ]
    },

    // 增加更多城市...
    // 内蒙古天气...
    '内蒙古': [
        { day: '星期一', temp: 25, weather: 'sunny' },
        { day: '星期二', temp: 26, weather: 'partly-cloudy' },
        { day: '星期三', temp: 24, weather: 'cloudy' },
        { day: '星期四', temp: 22, weather: 'rainy' },
        { day: '星期五', temp: 23, weather: 'partly-cloudy' }
    ],
    '安徽': [
        { day: '星期一', temp: 28, weather: 'partly-cloudy' },
        { day: '星期二', temp: 29, weather: 'sunny' },
        { day: '星期三', temp: 27, weather: 'rainy' },
        { day: '星期四', temp: 25, weather: 'thunderstorm' },
        { day: '星期五', temp: 26, weather: 'partly-cloudy' }
    ],
};

// 天气图标映射
const weatherIcons = {
    'sunny': 'fa-sun',
    'partly-cloudy': 'fa-cloud-sun',
    'cloudy': 'fa-cloud',
    'rainy': 'fa-cloud-rain',
    'thunderstorm': 'fa-bolt',
    'snowy': 'fa-snowflake',
    'foggy': 'fa-smog'
};

// 天气描述映射（中文）
const weatherDescriptions = {
    'sunny': '晴天',
    'partly-cloudy': '多云',
    'cloudy': '阴天',
    'rainy': '雨天',
    'thunderstorm': '雷暴',
    'snowy': '雪天',
    'foggy': '雾天'
};

// 显示当前日期
function showCurrentDate() {
    const now = new Date();
    const year = now.getFullYear();
    const month = now.getMonth() + 1;
    const day = now.getDate();
    currentDate.textContent = `${year}年${month}月${day}日`;
}

// 显示天气图标
function showWeatherIcon(weatherType) {
    weatherIcon.className = `fas ${weatherIcons[weatherType] || 'fa-sun'}`;
}

// 更新当前天气
function updateCurrentWeather(city) {
    const data = citiesWeather[city];

    if (!data) {
        alert(`没有找到"${city}"的天气信息`);
        return false;
    }

    cityName.textContent = city;
    temperature.textContent = data.current.temp;
    humidity.textContent = data.current.humidity;
    windSpeed.textContent = data.current.windSpeed;
    showWeatherIcon(data.current.weather);

    return true;
}

// 生成天气预报卡片
function generateForecastCards(city) {
    const data = citiesWeather[city];
    if (!data) return;

    forecastContainer.innerHTML = '';

    data.forecast.forEach(day => {
        const card = document.createElement('div');
        card.className = 'forecast-card';

        card.innerHTML = `
            <h4>${day.day}</h4>
            <div class="forecast-icon">
                <i class="fas ${weatherIcons[day.weather] || 'fa-sun'}"></i>
            </div>
            <div class="forecast-temp">${day.temp}°C</div>
            <div class="forecast-desc">${weatherDescriptions[day.weather] || '晴天'}</div>
        `;

        forecastContainer.appendChild(card);
    });
}

// 搜索按钮点击事件
searchBtn.addEventListener('click', () => {
    const city = cityInput.value.trim();
    if (city === '') return;

    const found = updateCurrentWeather(city);
    if (found) {
        generateForecastCards(city);
    }
});

// 监听输入框的回车键事件，触发搜索功能
cityInput.addEventListener('keydown', function(event) {
    if (event.key === 'Enter') {
        event.preventDefault();
        searchBtn.dispatchEvent(new Event('click'));
    }
});

// 生成城市列表
function generateCityList() {
    // 清空列表
    cityList.innerHTML = '';

    // 获取过滤文本
    const filterText = cityFilter.value.toLowerCase();

    // 获取所有城市
    const cities = Object.keys(citiesWeather).filter(city =>
        city.toLowerCase().includes(filterText) && typeof citiesWeather[city] === 'object' && citiesWeather[city].current
    );

    // 生成列表项
    cities.forEach(city => {
        const li = document.createElement('li');
        const weatherType = citiesWeather[city].current.weather;

        li.innerHTML = `<i class="fas ${weatherIcons[weatherType] || 'fa-sun'}"></i>${city}`;
        li.dataset.city = city;

        // 添加点击事件
        li.addEventListener('click', () => {
            // 移除所有活动项
            document.querySelectorAll('.city-list li').forEach(item => {
                item.classList.remove('active');
            });

            // 添加活动类
            li.classList.add('active');

            // 更新天气信息
            updateCurrentWeather(city);
            generateForecastCards(city);
        });

        cityList.appendChild(li);
    });
}

// 城市过滤功能
function setupCityFilter() {
    cityFilter.addEventListener('input', generateCityList);
}

// 初始化应用
function initApp() {
    showCurrentDate();
    generateCityList();
    setupCityFilter();

    // 默认选中北京
    updateCurrentWeather('北京');
    generateForecastCards('北京');

    // 默认选中北京列表项
    setTimeout(() => {
        const beijingItem = document.querySelector('.city-list li[data-city="北京"]');
        if (beijingItem) {
            beijingItem.classList.add('active');
        }
    }, 100);
}

// 页面加载完成后初始化
window.addEventListener('load', initApp);
