body {
  font-family: "Microsoft YaHei", <PERSON><PERSON>, sans-serif;
  background: #faf8ef;
  color: #776e65;
  text-align: center;
  margin: 0;
  padding: 0;
}
#game-container {
  display: inline-block;
  margin-top: 40px;
  background: #bbada0;
  padding: 20px 30px 30px 30px;
  border-radius: 10px;
  box-shadow: 0 4px 20px #aaa;
}
#grid-container {
  display: grid;
  grid-template-columns: repeat(4, 80px);
  grid-template-rows: repeat(4, 80px);
  gap: 10px;
  background: #bbada0;
  padding: 10px;
  border-radius: 8px;
  margin-bottom: 20px;
}
.tile {
  width: 80px;
  height: 80px;
  background: #cdc1b4;
  border-radius: 5px;
  font-size: 2em;
  line-height: 80px;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background 0.2s, color 0.2s;
}
.tile-2    { background: #eee4da; color: #776e65; }
.tile-4    { background: #ede0c8; color: #776e65; }
.tile-8    { background: #f2b179; color: #f9f6f2; }
.tile-16   { background: #f59563; color: #f9f6f2; }
.tile-32   { background: #f67c5f; color: #f9f6f2; }
.tile-64   { background: #f65e3b; color: #f9f6f2; }
.tile-128  { background: #edcf72; color: #f9f6f2; }
.tile-256  { background: #edcc61; color: #f9f6f2; }
.tile-512  { background: #edc850; color: #f9f6f2; }
.tile-1024 { background: #edc53f; color: #f9f6f2; }
.tile-2048 { background: #edc22e; color: #f9f6f2; }
#score-container {
  font-size: 1.2em;
  margin-bottom: 10px;
}
#restart-btn {
  background: #8f7a66;
  color: #fff;
  border: none;
  border-radius: 4px;
  padding: 8px 20px;
  font-size: 1em;
  cursor: pointer;
  margin-top: 10px;
  transition: background 0.2s;
}
#restart-btn:hover {
  background: #a39489;
}

.back-home {
  margin-top: 20px;
}

.back-btn {
  display: inline-block;
  background: #2ecc71;
  color: #fff;
  border: none;
  border-radius: 4px;
  padding: 8px 20px;
  font-size: 1em;
  text-decoration: none;
  cursor: pointer;
  transition: background 0.2s, transform 0.2s;
}

.back-btn:hover {
  background: #27ae60;
  transform: translateY(-2px);
}