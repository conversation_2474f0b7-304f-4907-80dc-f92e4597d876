<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>天气预报</title>
    <link rel="stylesheet" href="style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
</head>
<body>
    <div class="app-container">
        <!-- 左侧城市列表 -->
        <div class="cities-sidebar">
            <h2>城市列表</h2>
            <div class="city-search">
                <input type="text" id="city-filter" placeholder="搜索城市...">
                <i class="fas fa-search"></i>
            </div>
            <ul class="city-list" id="city-list">
                <!-- 城市列表将通过JavaScript动态生成 -->
            </ul>
        </div>

        <!-- 主内容区 -->
        <div class="container">
            <h1>天气预报</h1>

            <div class="search-box">
                <input type="text" id="city-input" placeholder="输入城市名称...">
                <button id="search-btn"><i class="fas fa-search"></i></button>
            </div>

            <div class="weather-container">
                <div class="current-weather card">
                    <div class="weather-header">
                        <h2 id="city-name">北京</h2>
                        <p id="current-date">2025年4月12日</p>
                    </div>
                    <div class="weather-info">
                        <div class="temperature">
                            <span id="temperature">25</span>°C
                        </div>
                        <div class="weather-icon">
                            <i id="weather-icon" class="fas fa-sun"></i>
                        </div>
                    </div>
                    <div class="weather-details">
                        <p><i class="fas fa-tint"></i> 湿度: <span id="humidity">45</span>%</p>
                        <p><i class="fas fa-wind"></i> 风速: <span id="wind-speed">5.2</span> m/s</p>
                    </div>
                </div>

                <div class="forecast">
                    <h3>未来天气预报</h3>
                    <div class="forecast-cards" id="forecast-container">
                        <!-- 天气预报卡片将通过JavaScript动态生成 -->
                    </div>
                </div>
                
                <div class="back-home">
                    <a href="../index.html" class="back-btn">返回主菜单</a>
                </div>
            </div>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>