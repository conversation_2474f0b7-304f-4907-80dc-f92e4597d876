/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Arial', sans-serif;
}

body {
    background: linear-gradient(135deg, #72b1e3, #4b6cb7);
    min-height: 100vh;
    padding: 0;
    margin: 0;
    overflow-x: hidden;
}

/* 应用容器 */
.app-container {
    display: flex;
    min-height: 100vh;
    width: 100%;
}

/* 左侧城市列表 */
.cities-sidebar {
    width: 250px;
    background-color: rgba(255, 255, 255, 0.9);
    padding: 20px;
    box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
    overflow-y: auto;
    height: 100vh;
    position: sticky;
    top: 0;
}

.cities-sidebar h2 {
    color: #4b6cb7;
    margin-bottom: 20px;
    text-align: center;
    font-size: 1.5rem;
}

.city-search {
    position: relative;
    margin-bottom: 20px;
}

.city-search input {
    width: 100%;
    padding: 10px 15px;
    padding-right: 35px;
    border: 1px solid #ddd;
    border-radius: 20px;
    font-size: 14px;
    outline: none;
}

.city-search i {
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: #4b6cb7;
}

.city-list {
    list-style: none;
}

.city-list li {
    padding: 12px 15px;
    border-bottom: 1px solid #eee;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
}

.city-list li:hover {
    background-color: #f0f4f8;
    transform: translateX(5px);
}

.city-list li.active {
    background-color: #e3f2fd;
    color: #4b6cb7;
    font-weight: bold;
}

.city-list li i {
    margin-right: 10px;
    color: #f39c12;
}

/* 主内容区 */
.container {
    flex: 1;
    padding: 30px;
    max-width: 800px;
    margin: 0 auto;
}

h1 {
    color: white;
    text-align: center;
    margin-bottom: 30px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

/* 搜索框样式 */
.search-box {
    display: flex;
    margin-bottom: 30px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    border-radius: 50px;
    overflow: hidden;
}

#city-input {
    flex: 1;
    padding: 15px 20px;
    border: none;
    outline: none;
    font-size: 16px;
}

#search-btn {
    background-color: #4b6cb7;
    color: white;
    border: none;
    padding: 0 25px;
    cursor: pointer;
    transition: background-color 0.3s;
}

#search-btn:hover {
    background-color: #3a57a0;
}

/* 卡片共通样式 */
.card {
    background-color: rgba(255, 255, 255, 0.9);
    border-radius: 20px;
    padding: 25px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.3);
}

/* 当前天气卡片 */
.current-weather {
    margin-bottom: 30px;
}

.weather-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

#city-name {
    font-size: 28px;
    color: #333;
}

#current-date {
    color: #666;
}

.weather-info {
    display: flex;
    justify-content: space-around;
    align-items: center;
    margin-bottom: 25px;
}

.temperature {
    font-size: 48px;
    font-weight: bold;
    color: #4b6cb7;
}

.weather-icon i {
    font-size: 72px;
    color: #f39c12;
}

.weather-details {
    display: flex;
    justify-content: space-around;
}

.weather-details p {
    display: flex;
    align-items: center;
    color: #555;
}

.weather-details i {
    margin-right: 10px;
    color: #4b6cb7;
}

/* 返回主菜单按钮 */
.back-home {
    text-align: center;
    margin-top: 30px;
}

.back-btn {
    display: inline-block;
    background-color: #2ecc71;
    color: white;
    text-decoration: none;
    padding: 12px 25px;
    border-radius: 50px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease;
    font-weight: bold;
}

.back-btn:hover {
    background-color: #27ae60;
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);
}

/* 预报区域 */
.forecast h3 {
    color: white;
    margin-bottom: 20px;
    text-align: center;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.forecast-cards {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 15px;
}

.forecast-card {
    background-color: rgba(255, 255, 255, 0.9);
    border-radius: 15px;
    padding: 15px;
    text-align: center;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.forecast-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
}

.forecast-card h4 {
    color: #444;
    margin-bottom: 10px;
}

.forecast-card .forecast-icon i {
    font-size: 36px;
    color: #f39c12;
    margin: 10px 0;
}

.forecast-card .forecast-temp {
    font-size: 22px;
    font-weight: bold;
    color: #4b6cb7;
    margin-bottom: 5px;
}

.forecast-card .forecast-desc {
    color: #666;
    font-size: 14px;
}

/* 响应式设计 */
@media (max-width: 1024px) {
    .app-container {
        flex-direction: column;
    }

    .cities-sidebar {
        width: 100%;
        height: auto;
        max-height: 300px;
        position: relative;
    }

    .container {
        padding: 20px;
    }
}

@media (max-width: 768px) {
    .weather-info {
        flex-direction: column;
    }

    .temperature {
        margin-bottom: 20px;
    }

    .weather-details {
        flex-direction: column;
        gap: 10px;
    }

    .forecast-cards {
        grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    }

    .cities-sidebar {
        max-height: 250px;
    }
}

@media (max-width: 480px) {
    .weather-header {
        flex-direction: column;
        align-items: flex-start;
    }

    #city-name {
        margin-bottom: 5px;
    }

    .forecast-cards {
        grid-template-columns: repeat(2, 1fr);
    }

    .cities-sidebar {
        max-height: 200px;
    }

    .city-list li {
        padding: 8px 10px;
    }
}