/* 全局样式 */
@import url('https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700&display=swap');

:root {
    --primary-color: #1e88e5;
    --primary-dark: #1565c0;
    --secondary-color: #26a69a;
    --text-color: #333;
    --text-light: #666;
    --bg-color: #f8f9fa;
    --card-color: #fff;
    --border-color: #e0e0e0;
    --sidebar-width: 300px;
    --timeline-color: #e3f2fd;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Noto Sans SC', sans-serif;
    background-color: var(--bg-color);
    color: var(--text-color);
    line-height: 1.6;
}

h1, h2, h3 {
    color: var(--primary-dark);
    margin-bottom: 1rem;
}

ul {
    list-style-type: none;
}

a {
    color: var(--primary-color);
    text-decoration: none;
}

a:hover {
    text-decoration: underline;
}

.container {
    display: flex;
    max-width: 1200px;
    margin: 2rem auto;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
    border-radius: 5px;
    overflow: hidden;
}

/* 侧边栏样式 */
.sidebar {
    width: var(--sidebar-width);
    background: linear-gradient(to bottom, var(--primary-color), var(--primary-dark));
    color: white;
    padding: 2rem;
}

.profile {
    text-align: center;
    padding-bottom: 2rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    margin-bottom: 2rem;
}

.profile h1 {
    color: white;
    margin: 1rem 0 0.5rem;
    font-size: 1.8rem;
}

.profile .title {
    font-size: 1rem;
    font-weight: 300;
    opacity: 0.9;
}

.profile-img {
    width: 150px;
    height: 150px;
    border-radius: 50%;
    overflow: hidden;
    margin: 0 auto;
    border: 3px solid white;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.profile-img img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.contact-info, .skills, .languages {
    margin-bottom: 2rem;
}

.sidebar h2 {
    color: white;
    font-size: 1.2rem;
    margin-bottom: 1rem;
    position: relative;
    padding-bottom: 0.5rem;
}

.sidebar h2::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 50px;
    height: 2px;
    background-color: var(--secondary-color);
}

.contact-info ul li {
    margin-bottom: 0.7rem;
    display: flex;
    align-items: center;
}

.contact-info ul li i {
    margin-right: 10px;
    width: 20px;
    text-align: center;
    color: var(--secondary-color);
}

/* 技能样式 */
.skill {
    margin-bottom: 1rem;
}

.skill span {
    display: block;
    margin-bottom: 0.5rem;
}

.skill-bar {
    height: 8px;
    background-color: rgba(255, 255, 255, 0.2);
    border-radius: 4px;
    overflow: hidden;
}

.skill-progress {
    height: 100%;
    background-color: var(--secondary-color);
    border-radius: 4px;
}

/* 语言能力样式 */
.language {
    margin-bottom: 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.language-dots {
    display: flex;
}

.dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.2);
    margin-left: 5px;
}

.dot.filled {
    background-color: var(--secondary-color);
}

/* 主内容区样式 */
.content {
    flex: 1;
    padding: 2rem;
    background-color: var(--card-color);
}

.content section {
    margin-bottom: 2.5rem;
}

.content h2 {
    display: flex;
    align-items: center;
    font-size: 1.5rem;
    padding-bottom: 0.5rem;
    margin-bottom: 1.5rem;
    border-bottom: 2px solid var(--primary-color);
}

.content h2 i {
    margin-right: 10px;
    color: var(--primary-color);
}

/* 返回主菜单按钮 */
.back-to-home {
    text-align: center;
    margin-top: 2rem;
    margin-bottom: 1rem;
}

.back-btn {
    display: inline-block;
    background-color: var(--secondary-color);
    color: white;
    padding: 0.8rem 1.5rem;
    border-radius: 30px;
    font-weight: 500;
    transition: all 0.3s ease;
    text-decoration: none;
    box-shadow: 0 4px 10px rgba(38, 166, 154, 0.3);
}

.back-btn:hover {
    background-color: #1e8e82;
    transform: translateY(-3px);
    box-shadow: 0 6px 15px rgba(38, 166, 154, 0.4);
    text-decoration: none;
}

.about p {
    text-align: justify;
    line-height: 1.8;
}

/* 时间线样式 */
.timeline {
    position: relative;
}

.timeline::before {
    content: '';
    position: absolute;
    top: 0;
    bottom: 0;
    left: 120px;
    width: 2px;
    background-color: var(--timeline-color);
}

.timeline-item {
    position: relative;
    margin-bottom: 2rem;
    display: flex;
}

.timeline-date {
    width: 120px;
    text-align: right;
    padding-right: 20px;
    color: var(--primary-color);
    font-weight: 500;
}

.timeline-content {
    flex: 1;
    position: relative;
    padding-left: 30px;
}

.timeline-content::before {
    content: '';
    position: absolute;
    left: -6px;
    top: 5px;
    width: 14px;
    height: 14px;
    border-radius: 50%;
    background-color: var(--primary-color);
}

.timeline-content h3 {
    margin-bottom: 0.5rem;
    color: var(--primary-color);
}

.timeline-content p {
    margin-bottom: 0.5rem;
    color: var(--text-light);
}

.timeline-content ul {
    padding-left: 1.2rem;
    list-style-type: disc;
    margin-top: 0.8rem;
}

.timeline-content ul li {
    margin-bottom: 0.5rem;
}

/* 项目经历样式 */
.project-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1.5rem;
}

.project-card {
    background-color: var(--bg-color);
    padding: 1.5rem;
    border-radius: 5px;
    border-left: 3px solid var(--primary-color);
    transition: transform 0.3s, box-shadow 0.3s;
}

.project-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.project-card h3 {
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.project-date {
    color: var(--text-light);
    font-size: 0.9rem;
    margin-bottom: 0.8rem;
}

/* 论文与专利样式 */
.publication-list li {
    margin-bottom: 1.2rem;
    padding-bottom: 1.2rem;
    border-bottom: 1px solid var(--border-color);
}

.publication-list li:last-child {
    border-bottom: none;
}

.publication-title {
    font-weight: 500;
    margin-bottom: 0.3rem;
}

.publication-info {
    color: var(--text-light);
    font-size: 0.9rem;
}

/* 证书样式 */
.certificate-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 1.5rem;
}

.certificate {
    display: flex;
    align-items: flex-start;
    padding: 1rem;
    background-color: var(--bg-color);
    border-radius: 5px;
    transition: transform 0.3s;
}

.certificate:hover {
    transform: translateY(-5px);
}

.certificate i {
    font-size: 1.5rem;
    color: var(--primary-color);
    margin-right: 1rem;
}

.certificate h3 {
    font-size: 1rem;
    margin-bottom: 0.3rem;
}

.certificate p {
    color: var(--text-light);
    font-size: 0.9rem;
}

/* 响应式设计 */
@media (max-width: 900px) {
    .container {
        flex-direction: column;
        margin: 0;
        border-radius: 0;
    }
    
    .sidebar {
        width: 100%;
    }
    
    .timeline::before {
        left: 60px;
    }
    
    .timeline-date {
        width: 60px;
    }
    
    .project-grid, .certificate-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 600px) {
    .timeline-item {
        flex-direction: column;
    }
    
    .timeline::before {
        left: 20px;
    }
    
    .timeline-date {
        width: 100%;
        text-align: left;
        padding-left: 40px;
        margin-bottom: 0.5rem;
    }
    
    .timeline-content {
        padding-left: 40px;
    }
    
    .timeline-content::before {
        left: 13px;
    }
}