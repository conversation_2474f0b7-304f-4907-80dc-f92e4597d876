<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>小游戏与应用集合</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        /* 全局样式 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            --primary-color: #3498db;
            --secondary-color: #2ecc71;
            --accent-color: #f39c12;
            --dark-color: #2c3e50;
            --light-color: #ecf0f1;
            --danger-color: #e74c3c;
            --purple-color: #9b59b6;
            --transition: all 0.3s ease;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f5f7fa;
            color: var(--dark-color);
            line-height: 1.6;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        /* 头部样式 */
        header {
            text-align: center;
            margin-bottom: 40px;
            padding: 20px 0;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        header h1 {
            font-size: 2.5rem;
            color: var(--dark-color);
            margin-bottom: 10px;
        }

        header p {
            font-size: 1.2rem;
            color: #7f8c8d;
        }

        /* 游戏卡片网格 */
        .games-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 25px;
        }

        /* 游戏卡片样式 */
        .game-card {
            background-color: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            transition: var(--transition);
            position: relative;
            height: 100%;
            display: flex;
            flex-direction: column;
        }

        .game-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.15);
        }

        .card-header {
            height: 160px;
            position: relative;
            overflow: hidden;
        }

        .card-bg {
            width: 100%;
            height: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 4rem;
            color: white;
        }

        .bg-guess-number { background: linear-gradient(135deg, #3498db, #2980b9); }
        .bg-snake { background: linear-gradient(135deg, #2ecc71, #27ae60); }
        .bg-bingo { background: linear-gradient(135deg, #f39c12, #f1c40f); }
        .bg-weather { background: linear-gradient(135deg, #9b59b6, #8e44ad); }
        .bg-resume { background: linear-gradient(135deg, #e74c3c, #c0392b); }

        .card-content {
            padding: 20px;
            flex-grow: 1;
            display: flex;
            flex-direction: column;
        }

        .card-title {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 10px;
            color: var(--dark-color);
        }

        .card-description {
            color: #7f8c8d;
            margin-bottom: 20px;
            flex-grow: 1;
        }

        .card-footer {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .play-btn {
            display: inline-block;
            background-color: var(--primary-color);
            color: white;
            padding: 8px 16px;
            border-radius: 5px;
            text-decoration: none;
            font-weight: 500;
            transition: var(--transition);
        }

        .play-btn:hover {
            background-color: #2980b9;
            transform: scale(1.05);
        }

        .card-tag {
            background-color: var(--light-color);
            color: var(--dark-color);
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.8rem;
        }

        /* 页脚样式 */
        footer {
            text-align: center;
            margin-top: 50px;
            padding: 20px 0;
            color: #7f8c8d;
            font-size: 0.9rem;
        }

        /* 语言切换按钮 */
        .language-switch {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 100;
        }

        .language-btn {
            background-color: var(--primary-color);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 5px;
            cursor: pointer;
            font-weight: 500;
            transition: var(--transition);
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        }

        .language-btn:hover {
            background-color: #2980b9;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .games-grid {
                grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            }

            header h1 {
                font-size: 2rem;
            }

            header p {
                font-size: 1rem;
            }
        }

        @media (max-width: 480px) {
            .games-grid {
                grid-template-columns: 1fr;
            }

            .card-header {
                height: 120px;
            }
        }

        /* 动画效果 */
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .game-card {
            animation: fadeIn 0.5s ease forwards;
            opacity: 0;
        }

        .game-card:nth-child(1) { animation-delay: 0.1s; }
        .game-card:nth-child(2) { animation-delay: 0.2s; }
        .game-card:nth-child(3) { animation-delay: 0.3s; }
        .game-card:nth-child(4) { animation-delay: 0.4s; }
        .game-card:nth-child(5) { animation-delay: 0.5s; }
    </style>
</head>
<body>
    <div class="language-switch">
        <button class="language-btn" id="language-toggle">English</button>
    </div>

    <div class="container">
        <header>
            <h1>小游戏与应用集合</h1>
            <p>探索各种有趣的小游戏和实用应用</p>
        </header>

        <div class="games-grid">
            <!-- 猜数字游戏 -->
            <div class="game-card">
                <div class="card-header">
                    <div class="card-bg bg-guess-number">
                        <i class="fas fa-question-circle"></i>
                    </div>
                </div>
                <div class="card-content">
                    <h2 class="card-title">猜数字游戏</h2>
                    <p class="card-description">
                        尝试猜出1到100之间的随机数字。系统会提示你猜的数字是太大还是太小，看看你能用多少次猜对！
                    </p>
                    <div class="card-footer">
                        <a href="guessNumber/index.html" class="play-btn">开始游戏</a>
                        <span class="card-tag">休闲</span>
                    </div>
                </div>
            </div>

            <!-- 贪吃蛇游戏 -->
            <div class="game-card">
                <div class="card-header">
                    <div class="card-bg bg-snake">
                        <i class="fas fa-snake"></i>
                    </div>
                </div>
                <div class="card-content">
                    <h2 class="card-title">贪吃蛇游戏</h2>
                    <p class="card-description">
                        经典的贪吃蛇游戏！控制蛇移动吃到食物，让它不断变长。但要小心不要撞到墙壁或自己的身体！
                    </p>
                    <div class="card-footer">
                        <a href="snakeGame/index.html" class="play-btn">开始游戏</a>
                        <span class="card-tag">经典</span>
                    </div>
                </div>
            </div>

            <!-- Bingo游戏 -->
            <div class="game-card">
                <div class="card-header">
                    <div class="card-bg bg-bingo">
                        <i class="fas fa-th"></i>
                    </div>
                </div>
                <div class="card-content">
                    <h2 class="card-title">Bingo游戏</h2>
                    <p class="card-description">
                        传统的Bingo游戏！随机生成卡片，调用号码，当你完成一行、一列或对角线时，大声喊出"Bingo"！
                    </p>
                    <div class="card-footer">
                        <a href="bingo/index.html" class="play-btn">开始游戏</a>
                        <span class="card-tag">新游戏</span>
                    </div>
                </div>
            </div>

            <!-- 另一个猜数字游戏 -->
            <div class="game-card">
                <div class="card-header">
                    <div class="card-bg bg-guess-number" style="background: linear-gradient(135deg, #1abc9c, #16a085);">
                        <i class="fas fa-dice"></i>
                    </div>
                </div>
                <div class="card-content">
                    <h2 class="card-title">猜数字游戏 Pro</h2>
                    <p class="card-description">
                        增强版猜数字游戏，带有更多功能和更好的用户界面。挑战自己，打破最佳记录！
                    </p>
                    <div class="card-footer">
                        <a href="number/index.html" class="play-btn">开始游戏</a>
                        <span class="card-tag">进阶</span>
                    </div>
                </div>
            </div>

            <!-- 天气应用 -->
            <div class="game-card">
                <div class="card-header">
                    <div class="card-bg bg-weather">
                        <i class="fas fa-cloud-sun"></i>
                    </div>
                </div>
                <div class="card-content">
                    <h2 class="card-title">Weather Forecast</h2>
                    <p class="card-description">
                        Check weather forecasts for different cities, including current temperature, humidity, wind speed, and weather conditions for upcoming days.
                    </p>
                    <div class="card-footer">
                        <a href="weather/index.html" class="play-btn">Check Weather</a>
                        <span class="card-tag">Utility</span>
                    </div>
                </div>
            </div>

            <!-- 简历模板 -->
            <div class="game-card">
                <div class="card-header">
                    <div class="card-bg bg-resume">
                        <i class="fas fa-file-alt"></i>
                    </div>
                </div>
                <div class="card-content">
                    <h2 class="card-title">个人简历模板</h2>
                    <p class="card-description">
                        专业的个人简历模板，适合化学工程师使用。展示你的技能、经验和教育背景。
                    </p>
                    <div class="card-footer">
                        <a href="resume/index.html" class="play-btn">查看模板</a>
                        <span class="card-tag">模板</span>
                    </div>
                </div>
            </div>
            <!-- 2048 游戏 -->
            <div class="game-card">
                <div class="card-header">
                    <div class="card-bg" style="background: linear-gradient(135deg, #ff9800, #ffb74d);">
                        <i class="fas fa-th-large"></i>
                    </div>
                </div>
                <div class="card-content">
                    <h2 class="card-title">2048 游戏</h2>
                    <p class="card-description">
                        经典数字合成益智游戏。通过方向键移动方块，相同数字合并，挑战合成2048！
                    </p>
                    <div class="card-footer">
                        <a href="2048/index.html" class="play-btn">开始游戏</a>
                        <span class="card-tag">益智</span>
                    </div>
                </div>
            </div>
            
            <!-- 记忆翻牌游戏 -->
            <div class="game-card">
                <div class="card-header">
                    <div class="card-bg" style="background: linear-gradient(135deg, #9b59b6, #8e44ad);">
                        <i class="fas fa-clone"></i>
                    </div>
                </div>
                <div class="card-content">
                    <h2 class="card-title">记忆翻牌游戏</h2>
                    <p class="card-description">
                        考验记忆力的经典翻牌游戏。翻开卡片找到配对，用最少的步数完成所有配对挑战！
                    </p>
                    <div class="card-footer">
                        <a href="memory-card/index.html" class="play-btn">开始游戏</a>
                        <span class="card-tag">记忆力</span>
                    </div>
                </div>
            </div>
        </div>

        <footer>
            <p>&copy; 2025 小游戏与应用集合 | 使用HTML, CSS和JavaScript构建</p>
        </footer>
    </div>

    <script>
        // 多语言支持
        const translations = {
            'zh': {
                'title': '小游戏与应用集合',
                'subtitle': '探索各种有趣的小游戏和实用应用',
                'guessNumber': {
                    'title': '猜数字游戏',
                    'description': '尝试猜出1到100之间的随机数字。系统会提示你猜的数字是太大还是太小，看看你能用多少次猜对！',
                    'button': '开始游戏',
                    'tag': '休闲'
                },
                'snake': {
                    'title': '贪吃蛇游戏',
                    'description': '经典的贪吃蛇游戏！控制蛇移动吃到食物，让它不断变长。但要小心不要撞到墙壁或自己的身体！',
                    'button': '开始游戏',
                    'tag': '经典'
                },
                'bingo': {
                    'title': 'Bingo游戏',
                    'description': '传统的Bingo游戏！随机生成卡片，调用号码，当你完成一行、一列或对角线时，大声喜出"Bingo"！',
                    'button': '开始游戏',
                    'tag': '新游戏'
                },
                'guessPro': {
                    'title': '猜数字游戏 Pro',
                    'description': '增强版猜数字游戏，带有更多功能和更好的用户界面。挑战自己，打破最佳记录！',
                    'button': '开始游戏',
                    'tag': '进阶'
                },
                'weather': {
                    'title': '天气预报',
                    'description': '查看不同城市的天气预报，包括当前温度、湿度、风速和未来几天的天气情况。',
                    'button': '查看天气',
                    'tag': '实用工具'
                },
                'resume': {
                    'title': '个人简历模板',
                    'description': '专业的个人简历模板，适合化学工程师使用。展示你的技能、经验和教育背景。',
                    'button': '查看模板',
                    'tag': '模板'
                },
                'footer': '&copy; 2025 小游戏与应用集合 | 使用HTML, CSS和JavaScript构建',
                'langButton': 'English',
                'game2048': {
                    'title': '2048 游戏',
                    'description': '经典数字合成益智游戏。通过方向键移动方块，相同数字合并，挑战合成2048！',
                    'button': '开始游戏',
                    'tag': '益智'
                },
                'memoryCard': {
                    'title': '记忆翻牌游戏',
                    'description': '考验记忆力的经典翻牌游戏。翻开卡片找到配对，用最少的步数完成所有配对挑战！',
                    'button': '开始游戏',
                    'tag': '记忆力'
                }
            },
            'en': {
                'title': 'Games & Apps Collection',
                'subtitle': 'Explore various fun games and useful applications',
                'guessNumber': {
                    'title': 'Number Guessing Game',
                    'description': 'Try to guess a random number between 1 and 100. The system will tell you if your guess is too high or too low. See how many attempts you need!',
                    'button': 'Play Game',
                    'tag': 'Casual'
                },
                'snake': {
                    'title': 'Snake Game',
                    'description': 'The classic Snake game! Control the snake to eat food and grow longer. But be careful not to hit the walls or your own body!',
                    'button': 'Play Game',
                    'tag': 'Classic'
                },
                'bingo': {
                    'title': 'Bingo Game',
                    'description': 'Traditional Bingo game! Random card generation, number calling, and when you complete a row, column, or diagonal, shout "Bingo"!',
                    'button': 'Play Game',
                    'tag': 'New Game'
                },
                'guessPro': {
                    'title': 'Number Guessing Pro',
                    'description': 'Enhanced version of the number guessing game with more features and a better user interface. Challenge yourself and break your best record!',
                    'button': 'Play Game',
                    'tag': 'Advanced'
                },
                'weather': {
                    'title': 'Weather Forecast',
                    'description': 'Check weather forecasts for different cities, including current temperature, humidity, wind speed, and weather conditions for upcoming days.',
                    'button': 'Check Weather',
                    'tag': 'Utility'
                },
                'resume': {
                    'title': 'Resume Template',
                    'description': 'Professional resume template for chemical engineers. Showcase your skills, experience, and educational background.',
                    'button': 'View Template',
                    'tag': 'Template'
                },
                'footer': '&copy; 2025 Games & Apps Collection | Built with HTML, CSS and JavaScript',
                'langButton': '中文',
                'game2048': {
                    'title': '2048',
                    'description': 'Classic number merging puzzle game. Move tiles with arrow keys, merge same numbers, and try to reach 2048!',
                    'button': 'Play Game',
                    'tag': 'Puzzle'
                },
                'memoryCard': {
                    'title': 'Memory Card Game',
                    'description': 'Classic memory matching game. Flip cards to find matching pairs and complete all matches with the fewest moves!',
                    'button': 'Play Game',
                    'tag': 'Memory'
                }
            }
        };

        // 当前语言
        let currentLang = 'zh';

        // DOM元素
        const languageToggle = document.getElementById('language-toggle');
        const pageTitle = document.querySelector('header h1');
        const pageSubtitle = document.querySelector('header p');
        const footer = document.querySelector('footer p');

        // 游戏卡片元素
        const gameCards = document.querySelectorAll('.game-card');

        // 切换语言函数
        function toggleLanguage() {
            // 切换语言
            currentLang = currentLang === 'zh' ? 'en' : 'zh';

            // 更新按钮文本
            languageToggle.textContent = translations[currentLang].langButton;

            // 更新页面标题和副标题
            pageTitle.textContent = translations[currentLang].title;
            pageSubtitle.textContent = translations[currentLang].subtitle;

            // 更新页脚
            footer.innerHTML = translations[currentLang].footer;

            // 更新游戏卡片
            updateGameCards();
            // 2048 游戏
            updateCardContent(gameCards[6], 'game2048');
            // 记忆翻牌游戏
            updateCardContent(gameCards[7], 'memoryCard');
        }

        // 更新游戏卡片函数
        function updateGameCards() {
            // 猜数字游戏
            updateCardContent(gameCards[0], 'guessNumber');

            // 贪吃蛇游戏
            updateCardContent(gameCards[1], 'snake');

            // Bingo游戏
            updateCardContent(gameCards[2], 'bingo');

            // 猜数字游戏 Pro
            updateCardContent(gameCards[3], 'guessPro');

            // 天气预报
            updateCardContent(gameCards[4], 'weather');

            // 简历模板
            updateCardContent(gameCards[5], 'resume');

            // 2048游戏
            updateCardContent(gameCards[6], 'game2048');
            
            // 记忆翻牌游戏
            updateCardContent(gameCards[7], 'memoryCard');
        }

        // 更新卡片内容函数
        function updateCardContent(card, key) {
            const title = card.querySelector('.card-title');
            const description = card.querySelector('.card-description');
            const button = card.querySelector('.play-btn');
            const tag = card.querySelector('.card-tag');

            title.textContent = translations[currentLang][key].title;
            description.textContent = translations[currentLang][key].description;
            button.textContent = translations[currentLang][key].button;
            tag.textContent = translations[currentLang][key].tag;
        }

        // 添加语言切换事件监听器
        languageToggle.addEventListener('click', toggleLanguage);

        // 修复Font Awesome图标问题
        document.addEventListener('DOMContentLoaded', function() {
            // 贪吃蛇图标修复（Font Awesome没有蛇图标）
            const snakeIcon = document.querySelector('.fa-snake');
            if (snakeIcon) {
                snakeIcon.classList.remove('fa-snake');
                snakeIcon.classList.add('fa-dragon');
            }
        });
    </script>
</body>
</html>
