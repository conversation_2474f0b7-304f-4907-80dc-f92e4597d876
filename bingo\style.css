/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Poppins', sans-serif;
    background-color: #f5f7fa;
    color: #333;
    line-height: 1.6;
}

.container {
    max-width: 900px;
    margin: 0 auto;
    padding: 20px;
}

h1 {
    text-align: center;
    color: #2c3e50;
    margin-bottom: 20px;
    font-size: 2.5rem;
}

button {
    background-color: #3498db;
    color: white;
    border: none;
    padding: 10px 15px;
    border-radius: 5px;
    cursor: pointer;
    font-size: 1rem;
    transition: background-color 0.3s;
}

button:hover {
    background-color: #2980b9;
}

button:disabled {
    background-color: #95a5a6;
    cursor: not-allowed;
}

/* 游戏控制区域 */
.game-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    background-color: #ecf0f1;
    padding: 15px;
    border-radius: 8px;
}

.game-info {
    display: flex;
    gap: 20px;
}

.game-info p {
    font-size: 1.1rem;
}

/* 游戏区域 */
.game-area {
    display: flex;
    gap: 20px;
    margin-bottom: 30px;
}

@media (max-width: 768px) {
    .game-area {
        flex-direction: column;
    }
}

/* Bingo 卡片样式 */
.bingo-board {
    flex: 1;
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.board-header {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    background-color: #3498db;
    color: white;
    font-weight: bold;
    font-size: 1.5rem;
}

.board-header div {
    padding: 10px;
    text-align: center;
}

.board-grid {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    grid-template-rows: repeat(5, 1fr);
    gap: 2px;
    background-color: #ecf0f1;
    padding: 2px;
}

.grid-cell {
    background-color: white;
    aspect-ratio: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 1.3rem;
    font-weight: bold;
    transition: all 0.3s;
}

.free-cell {
    background-color: #f39c12;
    color: white;
}

.marked {
    background-color: #2ecc71;
    color: white;
    transform: scale(0.95);
}

/* 号码池区域 */
.number-pool {
    width: 250px;
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    padding: 15px;
    display: flex;
    flex-direction: column;
}

.number-pool h3 {
    text-align: center;
    margin-bottom: 10px;
    color: #2c3e50;
}

.called-numbers {
    display: flex;
    flex-wrap: wrap;
    gap: 5px;
    margin-bottom: 15px;
    min-height: 200px;
    overflow-y: auto;
}

.number-ball {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    font-weight: bold;
    color: white;
}

.ball-b { background-color: #e74c3c; }
.ball-i { background-color: #3498db; }
.ball-n { background-color: #2ecc71; }
.ball-g { background-color: #f39c12; }
.ball-o { background-color: #9b59b6; }

#call-number-btn {
    margin-top: auto;
}

/* 说明区域 */
.instructions {
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    padding: 15px;
    margin-bottom: 20px;
}

.instructions h3 {
    color: #2c3e50;
    margin-bottom: 10px;
}

.instructions ul {
    padding-left: 20px;
}

/* 返回主菜单 */
.back-home {
    text-align: center;
    margin-top: 20px;
    margin-bottom: 10px;
}

.back-btn {
    display: inline-block;
    background-color: #2ecc71;
    color: white;
    text-decoration: none;
    padding: 10px 20px;
    border-radius: 5px;
    font-size: 1rem;
    transition: background-color 0.3s, transform 0.3s;
}

.back-btn:hover {
    background-color: #27ae60;
    transform: translateY(-2px);
}

/* 获胜消息 */
.winner-message {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 100;
    color: white;
}

.winner-message h2 {
    font-size: 4rem;
    margin-bottom: 20px;
    color: #f1c40f;
}

.winner-message p {
    font-size: 1.5rem;
    margin-bottom: 30px;
}

.winner-message button {
    font-size: 1.2rem;
    padding: 12px 24px;
}

.hidden {
    display: none;
}
