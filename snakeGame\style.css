body {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    margin: 0;
    background: linear-gradient(135deg, #e0c3fc 0%, #8ec5fc 100%);
    font-family: Arial, sans-serif;
}

.game-container {
    background-color: rgba(255, 255, 255, 0.9);
    padding: 30px;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    text-align: center;
    max-width: 500px;
    width: 100%;
}

h1 {
    color: #2c3e50;
    margin: 0 0 20px 0;
    font-size: 2.2em;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
}

#game-info {
    display: flex;
    justify-content: space-between;
    margin-bottom: 15px;
    font-size: 1.1em;
    color: #34495e;
    font-weight: bold;
}

canvas {
    border: 3px solid #2c3e50;
    border-radius: 5px;
    background-color: #ecf0f1;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

#controls {
    margin-top: 20px;
}

button {
    background-color: #3498db;
    color: white;
    border: none;
    padding: 12px 25px;
    font-size: 1.1em;
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    margin: 10px;
}

button:hover {
    background-color: #2980b9;
    transform: translateY(-2px);
    box-shadow: 0 6px 8px rgba(0, 0, 0, 0.2);
}

.instructions {
    margin-top: 15px;
    color: #7f8c8d;
    line-height: 1.4;
    font-size: 0.9em;
}

.instructions p {
    margin: 5px 0;
}

#gameOver {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: rgba(255, 255, 255, 0.95);
    padding: 30px;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    text-align: center;
    backdrop-filter: blur(5px);
    border: 2px solid rgba(255, 255, 255, 0.1);
    animation: fadeIn 0.3s ease-out;
}

#gameOver h2 {
    color: #e74c3c;
    margin: 0 0 15px 0;
    font-size: 2em;
}

#gameOver p {
    color: #2c3e50;
    font-size: 1.2em;
    margin: 15px 0;
}

.hidden {
    display: none;
}

.back-home {
    margin-top: 20px;
}

.back-btn {
    display: inline-block;
    background-color: #2ecc71;
    color: white;
    text-decoration: none;
    padding: 10px 20px;
    border-radius: 25px;
    font-size: 1em;
    transition: all 0.3s ease;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.back-btn:hover {
    background-color: #27ae60;
    transform: translateY(-2px);
    box-shadow: 0 6px 8px rgba(0, 0, 0, 0.2);
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translate(-50%, -60%);
    }
    to {
        opacity: 1;
        transform: translate(-50%, -50%);
    }
}