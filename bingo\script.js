// 游戏变量
let bingoCard = [];
let calledNumbers = [];
let gameActive = false;
let winningCombinations = [];

// DOM元素
const bingoGrid = document.getElementById('bingo-grid');
const callNumberBtn = document.getElementById('call-number-btn');
const newGameBtn = document.getElementById('new-game-btn');
const playAgainBtn = document.getElementById('play-again-btn');
const calledNumbersContainer = document.getElementById('called-numbers-container');
const callCountElement = document.getElementById('call-count');
const lastNumberElement = document.getElementById('last-number');
const winnerMessage = document.getElementById('winner-message');

// 初始化游戏
function initGame() {
    // 重置游戏状态
    bingoCard = [];
    calledNumbers = [];
    gameActive = true;
    winningCombinations = [];
    
    // 重置UI
    bingoGrid.innerHTML = '';
    calledNumbersContainer.innerHTML = '';
    callCountElement.textContent = '0';
    lastNumberElement.textContent = '-';
    winnerMessage.classList.add('hidden');
    
    // 启用/禁用按钮
    callNumberBtn.disabled = false;
    newGameBtn.disabled = false;
    
    // 生成Bingo卡片
    generateBingoCard();
    
    // 渲染Bingo卡片
    renderBingoCard();
    
    // 设置获胜组合
    setupWinningCombinations();
}

// 生成Bingo卡片
function generateBingoCard() {
    // B列 (1-15)
    const bColumn = getRandomNumbers(1, 15, 5);
    // I列 (16-30)
    const iColumn = getRandomNumbers(16, 30, 5);
    // N列 (31-45)，中间是免费格
    const nColumn = getRandomNumbers(31, 45, 4);
    // G列 (46-60)
    const gColumn = getRandomNumbers(46, 60, 5);
    // O列 (61-75)
    const oColumn = getRandomNumbers(61, 75, 5);
    
    // 组合成5x5的卡片
    for (let row = 0; row < 5; row++) {
        bingoCard[row] = [];
        bingoCard[row][0] = bColumn[row];
        bingoCard[row][1] = iColumn[row];
        
        if (row === 2) {
            // 中间是免费格
            bingoCard[row][2] = 'FREE';
        } else {
            bingoCard[row][2] = nColumn[row < 2 ? row : row - 1];
        }
        
        bingoCard[row][3] = gColumn[row];
        bingoCard[row][4] = oColumn[row];
    }
}

// 获取指定范围内的随机不重复数字
function getRandomNumbers(min, max, count) {
    const numbers = [];
    const availableNumbers = Array.from({ length: max - min + 1 }, (_, i) => min + i);
    
    for (let i = 0; i < count; i++) {
        const randomIndex = Math.floor(Math.random() * availableNumbers.length);
        numbers.push(availableNumbers[randomIndex]);
        availableNumbers.splice(randomIndex, 1);
    }
    
    return numbers;
}

// 渲染Bingo卡片
function renderBingoCard() {
    for (let row = 0; row < 5; row++) {
        for (let col = 0; col < 5; col++) {
            const cell = document.createElement('div');
            cell.className = 'grid-cell';
            cell.dataset.row = row;
            cell.dataset.col = col;
            
            if (bingoCard[row][col] === 'FREE') {
                cell.textContent = 'FREE';
                cell.classList.add('free-cell', 'marked');
            } else {
                cell.textContent = bingoCard[row][col];
            }
            
            bingoGrid.appendChild(cell);
        }
    }
}

// 设置获胜组合
function setupWinningCombinations() {
    // 行
    for (let row = 0; row < 5; row++) {
        const rowCells = [];
        for (let col = 0; col < 5; col++) {
            rowCells.push({ row, col });
        }
        winningCombinations.push(rowCells);
    }
    
    // 列
    for (let col = 0; col < 5; col++) {
        const colCells = [];
        for (let row = 0; row < 5; row++) {
            colCells.push({ row, col });
        }
        winningCombinations.push(colCells);
    }
    
    // 对角线 (左上到右下)
    const diagonal1 = [];
    for (let i = 0; i < 5; i++) {
        diagonal1.push({ row: i, col: i });
    }
    winningCombinations.push(diagonal1);
    
    // 对角线 (右上到左下)
    const diagonal2 = [];
    for (let i = 0; i < 5; i++) {
        diagonal2.push({ row: i, col: 4 - i });
    }
    winningCombinations.push(diagonal2);
}

// 调用号码
function callNumber() {
    if (!gameActive) return;
    
    // 检查是否所有号码都已调用
    if (calledNumbers.length >= 75) {
        alert('所有号码都已调用！');
        return;
    }
    
    // 生成1-75之间的随机数，确保不重复
    let number;
    do {
        number = Math.floor(Math.random() * 75) + 1;
    } while (calledNumbers.includes(number));
    
    // 添加到已调用号码列表
    calledNumbers.push(number);
    
    // 更新UI
    updateCalledNumbersUI(number);
    
    // 检查卡片上是否有这个号码，如果有则标记
    markNumberOnCard(number);
    
    // 检查是否获胜
    checkForWin();
}

// 更新已调用号码的UI
function updateCalledNumbersUI(number) {
    // 更新计数和最后一个号码
    callCountElement.textContent = calledNumbers.length;
    lastNumberElement.textContent = getLetterForNumber(number) + number;
    
    // 创建号码球
    const numberBall = document.createElement('div');
    numberBall.className = `number-ball ball-${getLetterForNumber(number).toLowerCase()}`;
    numberBall.textContent = number;
    
    // 添加到容器
    calledNumbersContainer.prepend(numberBall);
}

// 根据号码获取对应的字母
function getLetterForNumber(number) {
    if (number <= 15) return 'B';
    if (number <= 30) return 'I';
    if (number <= 45) return 'N';
    if (number <= 60) return 'G';
    return 'O';
}

// 在卡片上标记号码
function markNumberOnCard(number) {
    // 遍历卡片查找号码
    for (let row = 0; row < 5; row++) {
        for (let col = 0; col < 5; col++) {
            if (bingoCard[row][col] === number) {
                // 找到号码，标记它
                const cell = bingoGrid.querySelector(`[data-row="${row}"][data-col="${col}"]`);
                cell.classList.add('marked');
                return;
            }
        }
    }
}

// 检查是否获胜
function checkForWin() {
    for (const combination of winningCombinations) {
        let allMarked = true;
        
        for (const { row, col } of combination) {
            const cell = bingoGrid.querySelector(`[data-row="${row}"][data-col="${col}"]`);
            if (!cell.classList.contains('marked')) {
                allMarked = false;
                break;
            }
        }
        
        if (allMarked) {
            // 玩家获胜
            gameActive = false;
            callNumberBtn.disabled = true;
            
            // 高亮获胜组合
            highlightWinningCombination(combination);
            
            // 显示获胜消息
            setTimeout(() => {
                winnerMessage.classList.remove('hidden');
            }, 1000);
            
            return;
        }
    }
}

// 高亮获胜组合
function highlightWinningCombination(combination) {
    for (const { row, col } of combination) {
        const cell = bingoGrid.querySelector(`[data-row="${row}"][data-col="${col}"]`);
        cell.style.backgroundColor = '#f1c40f';
        cell.style.color = '#2c3e50';
        cell.style.transform = 'scale(1)';
    }
}

// 事件监听器
callNumberBtn.addEventListener('click', callNumber);
newGameBtn.addEventListener('click', initGame);
playAgainBtn.addEventListener('click', initGame);

// 初始化游戏
initGame();
