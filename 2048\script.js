// 2048 游戏核心逻辑
const gridSize = 4;
let grid, score;

function initGame() {
  grid = Array.from({ length: gridSize }, () => Array(gridSize).fill(0));
  score = 0;
  addRandomTile();
  addRandomTile();
  updateGrid();
  updateScore();
}

function addRandomTile() {
  const empty = [];
  for (let r = 0; r < gridSize; r++) {
    for (let c = 0; c < gridSize; c++) {
      if (grid[r][c] === 0) empty.push([r, c]);
    }
  }
  if (empty.length === 0) return;
  const [r, c] = empty[Math.floor(Math.random() * empty.length)];
  grid[r][c] = Math.random() < 0.9 ? 2 : 4;
}

function updateGrid() {
  const container = document.getElementById('grid-container');
  container.innerHTML = '';
  for (let r = 0; r < gridSize; r++) {
    for (let c = 0; c < gridSize; c++) {
      const value = grid[r][c];
      const tile = document.createElement('div');
      tile.className = 'tile' + (value ? ' tile-' + value : '');
      tile.textContent = value ? value : '';
      container.appendChild(tile);
    }
  }
}

function updateScore() {
  document.getElementById('score').textContent = score;
}

function move(direction) {
  let moved = false;
  let merged = Array.from({ length: gridSize }, () => Array(gridSize).fill(false));
  function traverse(callback) {
    for (let r = 0; r < gridSize; r++) {
      for (let c = 0; c < gridSize; c++) {
        callback(r, c);
      }
    }
  }
  function getCell(r, c) {
    if (r < 0 || r >= gridSize || c < 0 || c >= gridSize) return null;
    return grid[r][c];
  }
  function setCell(r, c, val) {
    grid[r][c] = val;
  }
  let dr = 0, dc = 0;
  if (direction === 'left') dc = -1;
  if (direction === 'right') dc = 1;
  if (direction === 'up') dr = -1;
  if (direction === 'down') dr = 1;

  let range = [...Array(gridSize).keys()];
  if (direction === 'right' || direction === 'down') range = range.reverse();

  if (direction === 'left' || direction === 'right') {
    for (let r = 0; r < gridSize; r++) {
      for (let i of range) {
        let c = i;
        if (grid[r][c] === 0) continue;
        let nc = c;
        while (true) {
          let next = nc + dc;
          if (next < 0 || next >= gridSize) break;
          if (grid[r][next] === 0) {
            grid[r][next] = grid[r][nc];
            grid[r][nc] = 0;
            nc = next;
            moved = true;
          } else if (grid[r][next] === grid[r][nc] && !merged[r][next] && !merged[r][nc]) {
            grid[r][next] *= 2;
            score += grid[r][next];
            grid[r][nc] = 0;
            merged[r][next] = true;
            moved = true;
            break;
          } else {
            break;
          }
        }
      }
    }
  } else {
    for (let c = 0; c < gridSize; c++) {
      for (let i of range) {
        let r = i;
        if (grid[r][c] === 0) continue;
        let nr = r;
        while (true) {
          let next = nr + dr;
          if (next < 0 || next >= gridSize) break;
          if (grid[next][c] === 0) {
            grid[next][c] = grid[nr][c];
            grid[nr][c] = 0;
            nr = next;
            moved = true;
          } else if (grid[next][c] === grid[nr][c] && !merged[next][c] && !merged[nr][c]) {
            grid[next][c] *= 2;
            score += grid[next][c];
            grid[nr][c] = 0;
            merged[next][c] = true;
            moved = true;
            break;
          } else {
            break;
          }
        }
      }
    }
  }
  if (moved) {
    addRandomTile();
    updateGrid();
    updateScore();
    if (isGameOver()) {
      setTimeout(() => alert('游戏结束！'), 100);
    }
  }
}

function isGameOver() {
  for (let r = 0; r < gridSize; r++) {
    for (let c = 0; c < gridSize; c++) {
      if (grid[r][c] === 0) return false;
      if (c < gridSize - 1 && grid[r][c] === grid[r][c + 1]) return false;
      if (r < gridSize - 1 && grid[r][c] === grid[r + 1][c]) return false;
    }
  }
  return true;
}

document.addEventListener('keydown', e => {
  switch (e.key) {
    case 'ArrowLeft': move('left'); break;
    case 'ArrowRight': move('right'); break;
    case 'ArrowUp': move('up'); break;
    case 'ArrowDown': move('down'); break;
  }
});

document.getElementById('restart-btn').onclick = initGame;

window.onload = initGame;