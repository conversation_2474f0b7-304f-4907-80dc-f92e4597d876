const canvas = document.getElementById('gameCanvas');
const ctx = canvas.getContext('2d');
const startBtn = document.getElementById('startBtn');
const restartBtn = document.getElementById('restartBtn');
const scoreDisplay = document.getElementById('score');
const statusDisplay = document.getElementById('status');
const gameOverScreen = document.getElementById('gameOver');
const finalScore = document.getElementById('finalScore');

const box = 20;
let snake = [];
let food = {};
let score = 0;
let direction;
let game;
let gameState = 'ready';

/**
 * 初始化游戏状态。
 * 设置蛇的初始位置、食物的随机位置、分数归零，并重置方向。
 * 同时更新UI并绘制初始游戏画面。
 */
function initGame() {
    // 初始化蛇的位置，开始时只有一个头，位于网格中间偏左一点
    snake = [{x: 9 * box, y: 10 * box}];

    // 在随机位置生成第一个食物
    // Math.random() 生成 0 到 1 之间的随机数
    // Math.floor() 向下取整，确保坐标是 box 的整数倍
    // * 20 限制食物出现在 20x20 的网格内
    food = {
        x: Math.floor(Math.random() * 20) * box,
        y: Math.floor(Math.random() * 20) * box
    };

    // 将分数重置为 0
    score = 0;

    // 重置蛇的移动方向，防止游戏一开始就移动
    direction = undefined;

    // 调用函数更新界面显示（例如分数）
    updateUI();

    // 调用函数绘制初始的游戏画面（蛇、食物等）
    drawGame();
}
document.addEventListener('keydown', handleDirection);

function handleDirection(event) {
    if(gameState !== 'playing') return;

    if(event.keyCode == 37 && direction != "RIGHT") direction = "LEFT";
    else if(event.keyCode == 38 && direction != "DOWN") direction = "UP";
    else if(event.keyCode == 39 && direction != "LEFT") direction = "RIGHT";
    else if(event.keyCode == 40 && direction != "UP") direction = "DOWN";
}

startBtn.addEventListener('click', startGame);
restartBtn.addEventListener('click', restartGame);

function startGame() {
    if(gameState === 'playing') return;

    gameState = 'playing';
    startBtn.innerText = "游戏中...";
    startBtn.disabled = true;
    updateUI();

    if(game) clearInterval(game);
    game = setInterval(drawGame, 300);  // 从200改为300,让蛇移动速度更慢
}

function restartGame() {
    gameOverScreen.classList.add('hidden');
    initGame();
    startGame();
}

function drawGame() {
    ctx.fillStyle = "#ecf0f1";
    ctx.fillRect(0, 0, canvas.width, canvas.height);

    // 绘制网格(可选)
    ctx.strokeStyle = "rgba(0,0,0,0.1)";
    ctx.lineWidth = 0.5;
    for(let i = 0; i <= canvas.width; i += box) {
        ctx.beginPath();
        ctx.moveTo(i, 0);
        ctx.lineTo(i, canvas.height);
        ctx.stroke();
    }
    for(let i = 0; i <= canvas.height; i += box) {
        ctx.beginPath();
        ctx.moveTo(0, i);
        ctx.lineTo(canvas.width, i);
        ctx.stroke();
    }

    // 绘制蛇
    for(let i = 0; i < snake.length; i++) {
        ctx.fillStyle = i == 0 ? "#2ecc71" : "#27ae60";
        ctx.fillRect(snake[i].x, snake[i].y, box, box);

        ctx.strokeStyle = "rgba(255,255,255,0.8)";
        ctx.lineWidth = 1;
        ctx.strokeRect(snake[i].x, snake[i].y, box, box);
    }

    // 绘制食物
    ctx.fillStyle = "#e74c3c";
    ctx.beginPath();
    ctx.arc(food.x + box/2, food.y + box/2, box/2 - 2, 0, Math.PI * 2);
    ctx.fill();

    if(gameState !== 'playing') return;

    let snakeX = snake[0].x;
    let snakeY = snake[0].y;

    if(direction == "LEFT") snakeX -= box;
    if(direction == "UP") snakeY -= box;
    if(direction == "RIGHT") snakeX += box;
    if(direction == "DOWN") snakeY += box;

    if(snakeX == food.x && snakeY == food.y) {
        score++;
        food = {
            x: Math.floor(Math.random() * 20) * box,
            y: Math.floor(Math.random() * 20) * box
        };
    } else {
        snake.pop();
    }

    let newHead = {x: snakeX, y: snakeY};

    if(snakeX < 0 || snakeY < 0 || snakeX >= canvas.width || snakeY >= canvas.height
       || collision(newHead, snake)) {
        gameOver();
        return;
    }

    snake.unshift(newHead);
    updateUI();
}

function collision(head, array) {
    for(let i = 0; i < array.length; i++) {
        if(head.x == array[i].x && head.y == array[i].y) {
            return true;
        }
    }
    return false;
}

function updateUI() {
    scoreDisplay.innerText = "分数: " + score;
    statusDisplay.innerText = "状态: " +
        (gameState === 'playing' ? '游戏中' :
         gameState === 'over' ? '游戏结束' : '准备开始');
}

function gameOver() {
    clearInterval(game);
    gameState = 'over';
    startBtn.innerText = "开始游戏";
    startBtn.disabled = false;
    finalScore.innerText = score;
    gameOverScreen.classList.remove('hidden');
    updateUI();
}

// 初始化游戏
initGame();
