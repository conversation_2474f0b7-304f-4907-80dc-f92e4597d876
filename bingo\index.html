<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bingo游戏</title>
    <link rel="stylesheet" href="style.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <div class="container">
        <h1>Bingo 游戏</h1>
        
        <div class="game-controls">
            <button id="new-game-btn">开始新游戏</button>
            <div class="game-info">
                <p>已调用次数: <span id="call-count">0</span></p>
                <p>上次号码: <span id="last-number">-</span></p>
            </div>
        </div>
        
        <div class="game-area">
            <div class="bingo-board">
                <div class="board-header">
                    <div>B</div>
                    <div>I</div>
                    <div>N</div>
                    <div>G</div>
                    <div>O</div>
                </div>
                <div class="board-grid" id="bingo-grid">
                    <!-- 格子将通过JavaScript动态生成 -->
                </div>
            </div>
            
            <div class="number-pool">
                <h3>已调用号码</h3>
                <div class="called-numbers" id="called-numbers-container">
                    <!-- 已调用的号码将在这里显示 -->
                </div>
                <button id="call-number-btn">调用号码</button>
            </div>
        </div>
        
        <div class="instructions">
            <h3>游戏规则</h3>
            <ul>
                <li>每个Bingo卡片有5x5个格子，中间是免费格</li>
                <li>点击"调用号码"按钮抽取新号码</li>
                <li>如果你的卡片上有这个号码，它会被自动标记</li>
                <li>当你完成一行、一列或对角线时，你就赢了！</li>
            </ul>
        </div>
        
        <div id="winner-message" class="winner-message hidden">
            <h2>Bingo!</h2>
            <p>恭喜你赢了！</p>
            <button id="play-again-btn">再玩一次</button>
        </div>
        
        <div class="back-home">
            <a href="../index.html" class="back-btn">返回主菜单</a>
        </div>
    </div>
    
    <script src="script.js"></script>
</body>
</html>
