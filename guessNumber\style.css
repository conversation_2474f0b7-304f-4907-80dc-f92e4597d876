* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Microsoft YaHei', <PERSON><PERSON>, sans-serif;
}

body {
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    min-height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 20px;
}

.container {
    background-color: #fff;
    border-radius: 12px;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
    padding: 30px;
    width: 100%;
    max-width: 500px;
    text-align: center;
    transition: all 0.3s ease;
}

h1 {
    color: #333;
    margin-bottom: 15px;
    font-size: 2.2rem;
}

.game-description {
    color: #666;
    margin-bottom: 25px;
    font-size: 1.1rem;
}

.game-area {
    margin-bottom: 25px;
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 10px;
}

input[type="number"] {
    padding: 12px 15px;
    border: 2px solid #ddd;
    border-radius: 8px;
    font-size: 1.1rem;
    width: 100%;
    max-width: 200px;
    transition: border-color 0.3s;
    outline: none;
}

input[type="number"]:focus {
    border-color: #3498db;
}

button {
    padding: 12px 20px;
    border: none;
    border-radius: 8px;
    font-size: 1.1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: bold;
}

#guess-button {
    background-color: #3498db;
    color: white;
}

#guess-button:hover {
    background-color: #2980b9;
    transform: translateY(-2px);
}

#reset-button {
    background-color: #e74c3c;
    color: white;
}

#reset-button:hover {
    background-color: #c0392b;
    transform: translateY(-2px);
}

.message-area {
    margin-top: 25px;
    margin-bottom: 20px;
}

#message {
    font-size: 1.2rem;
    margin-bottom: 15px;
    min-height: 30px;
    padding: 5px;
    transition: all 0.3s ease;
}

.correct {
    color: #27ae60;
    font-weight: bold;
    animation: pulse 1s infinite;
}

.too-high {
    color: #e74c3c;
}

.too-low {
    color: #e67e22;
}

#guess-history {
    list-style: none;
    margin-top: 15px;
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 10px;
}

#guess-history li {
    background-color: #f1f1f1;
    padding: 5px 12px;
    border-radius: 20px;
    font-weight: bold;
    transition: transform 0.2s;
}

#guess-history li:hover {
    transform: scale(1.1);
}

.too-high-item {
    color: #e74c3c;
    border: 1px solid #e74c3c;
}

.too-low-item {
    color: #e67e22;
    border: 1px solid #e67e22;
}

.stats-area {
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px dashed #ddd;
}

h2 {
    color: #333;
    margin-bottom: 15px;
    font-size: 1.5rem;
}

.hidden {
    display: none;
}

.back-to-home {
    margin-top: 25px;
}

.back-btn {
    display: inline-block;
    background-color: #2ecc71;
    color: white;
    text-decoration: none;
    padding: 10px 20px;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: bold;
    transition: all 0.3s ease;
}

.back-btn:hover {
    background-color: #27ae60;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

/* 响应式调整 */
@media (max-width: 480px) {
    .container {
        padding: 20px;
    }
    
    h1 {
        font-size: 1.8rem;
    }
    
    .game-description {
        font-size: 1rem;
    }
    
    input[type="number"], button {
        font-size: 1rem;
        padding: 10px 15px;
        width: 100%;
    }
}