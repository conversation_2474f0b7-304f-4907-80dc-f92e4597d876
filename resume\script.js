// 检查个人照片是否存在，如果不存在则显示默认头像
document.addEventListener('DOMContentLoaded', function() {
    const profileImage = document.getElementById('profile-image');
    
    // 图片加载错误时的处理
    profileImage.onerror = function() {
        // 使用默认头像
        this.src = 'images/default-avatar.png';
        console.log('加载个人照片失败，已替换为默认头像');
    };

    // 添加平滑滚动效果
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            document.querySelector(this.getAttribute('href')).scrollIntoView({
                behavior: 'smooth'
            });
        });
    });

    // 初始化项目卡片和技能条的动画效果
    function animateOnScroll() {
        const elements = document.querySelectorAll('.project-card, .certificate, .skill-progress');
        elements.forEach(element => {
            const position = element.getBoundingClientRect();
            // 如果元素进入视口
            if(position.top < window.innerHeight && position.bottom >= 0) {
                element.classList.add('animated');
            }
        });
    }

    // 页面加载和滚动时触发动画
    window.addEventListener('scroll', animateOnScroll);
    animateOnScroll(); // 初始检查
});

// 打印简历功能
function printResume() {
    window.print();
}