/* 基础样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    background: linear-gradient(135deg, #0c0c0c 0%, #1a1a2e 50%, #16213e 100%);
    min-height: 100vh;
    color: #ffffff;
    overflow-x: hidden;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 2rem;
}

/* 头部样式 */
.header {
    text-align: center;
    margin-bottom: 3rem;
}

.title {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 1rem;
    background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4);
    background-size: 300% 300%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: gradientShift 4s ease-in-out infinite;
}

.title-icon {
    display: inline-block;
    margin-right: 1rem;
    animation: bounce 2s infinite;
}

.subtitle {
    font-size: 1.2rem;
    color: #a0a0a0;
    font-weight: 300;
}

/* 控制按钮 */
.controls {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-bottom: 3rem;
    flex-wrap: wrap;
}

.control-btn {
    background: rgba(255, 255, 255, 0.1);
    border: 2px solid rgba(255, 255, 255, 0.2);
    color: #ffffff;
    padding: 1rem 2rem;
    border-radius: 50px;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.control-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.4);
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
}

.control-btn.active {
    background: linear-gradient(45deg, #667eea 0%, #764ba2 100%);
    border-color: #667eea;
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.btn-icon {
    font-size: 1.2rem;
}

/* 天气卡片容器 */
.weather-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
}

/* 基础卡片样式 */
.weather-card {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 2rem;
    backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    position: relative;
    overflow: hidden;
    height: 400px;
    transition: all 0.5s ease;
    opacity: 0.3;
    transform: scale(0.95);
}

.weather-card.active {
    opacity: 1;
    transform: scale(1);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.card-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
}

.card-content {
    position: relative;
    z-index: 10;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
}

.weather-title {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.temperature {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 1rem;
}

.weather-info {
    display: flex;
    gap: 1rem;
    font-size: 0.9rem;
    color: #cccccc;
}

/* 晴天样式 */
.sunny {
    background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 50%, #fd79a8 100%);
}

.sun {
    width: 80px;
    height: 80px;
    background: radial-gradient(circle, #fff 0%, #ffd700 70%, #ff8c00 100%);
    border-radius: 50%;
    position: absolute;
    top: 20px;
    right: 20px;
    animation: sunPulse 3s ease-in-out infinite;
    box-shadow: 0 0 30px rgba(255, 215, 0, 0.6);
}

.sun-rays {
    position: absolute;
    top: 20px;
    right: 20px;
    width: 80px;
    height: 80px;
}

.ray {
    position: absolute;
    background: linear-gradient(90deg, transparent, #ffd700, transparent);
    width: 4px;
    height: 30px;
    top: 50%;
    left: 50%;
    transform-origin: 0 0;
    animation: rayRotate 8s linear infinite;
}

.ray-1 { transform: translate(-50%, -50%) rotate(0deg); }
.ray-2 { transform: translate(-50%, -50%) rotate(45deg); }
.ray-3 { transform: translate(-50%, -50%) rotate(90deg); }
.ray-4 { transform: translate(-50%, -50%) rotate(135deg); }
.ray-5 { transform: translate(-50%, -50%) rotate(180deg); }
.ray-6 { transform: translate(-50%, -50%) rotate(225deg); }
.ray-7 { transform: translate(-50%, -50%) rotate(270deg); }
.ray-8 { transform: translate(-50%, -50%) rotate(315deg); }

.clouds .cloud {
    background: rgba(255, 255, 255, 0.8);
    border-radius: 50px;
    position: absolute;
    animation: cloudFloat 6s ease-in-out infinite;
}

.cloud-1 {
    width: 60px;
    height: 30px;
    top: 60px;
    left: 20px;
    animation-delay: -2s;
}

.cloud-2 {
    width: 80px;
    height: 40px;
    top: 100px;
    left: 150px;
    animation-delay: -4s;
}

/* 雨天样式 */
.rainy {
    background: linear-gradient(135deg, #74b9ff 0%, #0984e3 50%, #2d3436 100%);
}

.rain-container {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
}

.rain-drop {
    position: absolute;
    width: 2px;
    height: 15px;
    background: linear-gradient(to bottom, transparent, #74b9ff, #0984e3);
    border-radius: 0 0 50% 50%;
    animation: rainFall 1s linear infinite;
}

.rain-drop:nth-child(1) { left: 10%; animation-delay: 0s; }
.rain-drop:nth-child(2) { left: 20%; animation-delay: 0.1s; }
.rain-drop:nth-child(3) { left: 30%; animation-delay: 0.2s; }
.rain-drop:nth-child(4) { left: 40%; animation-delay: 0.3s; }
.rain-drop:nth-child(5) { left: 50%; animation-delay: 0.4s; }
.rain-drop:nth-child(6) { left: 60%; animation-delay: 0.5s; }
.rain-drop:nth-child(7) { left: 70%; animation-delay: 0.6s; }
.rain-drop:nth-child(8) { left: 80%; animation-delay: 0.7s; }
.rain-drop:nth-child(9) { left: 90%; animation-delay: 0.8s; }
.rain-drop:nth-child(10) { left: 95%; animation-delay: 0.9s; }

.puddles .puddle {
    position: absolute;
    bottom: 20px;
    background: rgba(116, 185, 255, 0.3);
    border-radius: 50%;
    animation: puddleRipple 2s ease-in-out infinite;
}

.puddle-1 {
    width: 40px;
    height: 8px;
    left: 30px;
}

.puddle-2 {
    width: 60px;
    height: 12px;
    right: 40px;
    animation-delay: -1s;
}

.rain-clouds .rain-cloud {
    background: rgba(45, 52, 54, 0.8);
    border-radius: 30px;
    position: absolute;
    animation: cloudDrift 8s ease-in-out infinite;
}

.rain-cloud-1 {
    width: 80px;
    height: 40px;
    top: 20px;
    left: 10px;
}

.rain-cloud-2 {
    width: 100px;
    height: 50px;
    top: 40px;
    right: 20px;
    animation-delay: -3s;
}

/* 动画定义 */
@keyframes gradientShift {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-10px); }
    60% { transform: translateY(-5px); }
}

@keyframes sunPulse {
    0%, 100% { transform: scale(1); box-shadow: 0 0 30px rgba(255, 215, 0, 0.6); }
    50% { transform: scale(1.1); box-shadow: 0 0 50px rgba(255, 215, 0, 0.8); }
}

@keyframes rayRotate {
    0% { transform: translate(-50%, -50%) rotate(0deg); }
    100% { transform: translate(-50%, -50%) rotate(360deg); }
}

@keyframes cloudFloat {
    0%, 100% { transform: translateX(0) translateY(0); }
    33% { transform: translateX(10px) translateY(-5px); }
    66% { transform: translateX(-5px) translateY(5px); }
}

@keyframes rainFall {
    0% { top: -20px; opacity: 0; }
    10% { opacity: 1; }
    90% { opacity: 1; }
    100% { top: 100%; opacity: 0; }
}

@keyframes puddleRipple {
    0%, 100% { transform: scale(1); opacity: 0.3; }
    50% { transform: scale(1.2); opacity: 0.6; }
}

@keyframes cloudDrift {
    0%, 100% { transform: translateX(0); }
    50% { transform: translateX(20px); }
}

/* 雪天样式 */
.snowy {
    background: linear-gradient(135deg, #ddd6fe 0%, #a5b4fc 50%, #6366f1 100%);
}

.snow-container {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
}

.snowflake {
    position: absolute;
    color: #ffffff;
    font-size: 1.2rem;
    animation: snowFall 3s linear infinite;
    opacity: 0.8;
}

.snowflake:nth-child(1) { left: 10%; animation-delay: 0s; animation-duration: 3s; }
.snowflake:nth-child(2) { left: 20%; animation-delay: 0.5s; animation-duration: 3.5s; }
.snowflake:nth-child(3) { left: 30%; animation-delay: 1s; animation-duration: 2.5s; }
.snowflake:nth-child(4) { left: 40%; animation-delay: 1.5s; animation-duration: 4s; }
.snowflake:nth-child(5) { left: 50%; animation-delay: 2s; animation-duration: 3.2s; }
.snowflake:nth-child(6) { left: 60%; animation-delay: 0.3s; animation-duration: 2.8s; }
.snowflake:nth-child(7) { left: 70%; animation-delay: 0.8s; animation-duration: 3.7s; }
.snowflake:nth-child(8) { left: 80%; animation-delay: 1.3s; animation-duration: 2.3s; }

.snow-ground {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 30px;
    background: linear-gradient(to top, rgba(255, 255, 255, 0.8), transparent);
    border-radius: 0 0 20px 20px;
    animation: snowAccumulate 4s ease-in-out infinite;
}

.snow-clouds .snow-cloud {
    background: rgba(255, 255, 255, 0.6);
    border-radius: 40px;
    position: absolute;
    animation: cloudFloat 10s ease-in-out infinite;
}

.snow-cloud-1 {
    width: 90px;
    height: 45px;
    top: 30px;
    left: 20px;
}

.snow-cloud-2 {
    width: 70px;
    height: 35px;
    top: 50px;
    right: 30px;
    animation-delay: -4s;
}

/* 风天样式 */
.windy {
    background: linear-gradient(135deg, #81ecec 0%, #74b9ff 50%, #0984e3 100%);
}

.wind-lines {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
}

.wind-line {
    position: absolute;
    height: 2px;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.6), transparent);
    border-radius: 2px;
    animation: windFlow 2s ease-in-out infinite;
}

.wind-line-1 {
    top: 25%;
    width: 60px;
    left: 10%;
    animation-delay: 0s;
}

.wind-line-2 {
    top: 35%;
    width: 80px;
    left: 20%;
    animation-delay: 0.3s;
}

.wind-line-3 {
    top: 45%;
    width: 70px;
    left: 15%;
    animation-delay: 0.6s;
}

.wind-line-4 {
    top: 55%;
    width: 90px;
    left: 25%;
    animation-delay: 0.9s;
}

.wind-clouds .wind-cloud {
    background: rgba(255, 255, 255, 0.7);
    border-radius: 30px;
    position: absolute;
    animation: windCloudMove 3s ease-in-out infinite;
}

.wind-cloud-1 {
    width: 60px;
    height: 30px;
    top: 20px;
    left: 10px;
}

.wind-cloud-2 {
    width: 80px;
    height: 40px;
    top: 40px;
    left: 100px;
    animation-delay: -1s;
}

.wind-cloud-3 {
    width: 50px;
    height: 25px;
    top: 70px;
    right: 20px;
    animation-delay: -2s;
}

.trees {
    position: absolute;
    bottom: 20px;
    left: 0;
    right: 0;
}

.tree {
    position: absolute;
    bottom: 0;
    width: 8px;
    height: 60px;
    background: #8b4513;
    border-radius: 4px;
    animation: treeSway 1.5s ease-in-out infinite;
    transform-origin: bottom center;
}

.tree::before {
    content: '';
    position: absolute;
    top: -20px;
    left: -10px;
    width: 28px;
    height: 40px;
    background: #228b22;
    border-radius: 50% 50% 50% 50% / 60% 60% 40% 40%;
    animation: leavesRustle 1.5s ease-in-out infinite;
}

.tree-1 {
    left: 30px;
}

.tree-2 {
    right: 40px;
    animation-delay: -0.5s;
}

/* 雪天动画 */
@keyframes snowFall {
    0% {
        top: -20px;
        opacity: 0;
        transform: translateX(0) rotate(0deg);
    }
    10% {
        opacity: 1;
    }
    90% {
        opacity: 1;
    }
    100% {
        top: 100%;
        opacity: 0;
        transform: translateX(50px) rotate(360deg);
    }
}

@keyframes snowAccumulate {
    0%, 100% { height: 30px; }
    50% { height: 35px; }
}

/* 风天动画 */
@keyframes windFlow {
    0% {
        transform: translateX(-100px);
        opacity: 0;
    }
    50% {
        opacity: 1;
    }
    100% {
        transform: translateX(200px);
        opacity: 0;
    }
}

@keyframes windCloudMove {
    0%, 100% {
        transform: translateX(0);
    }
    50% {
        transform: translateX(30px);
    }
}

@keyframes treeSway {
    0%, 100% {
        transform: rotate(0deg);
    }
    25% {
        transform: rotate(3deg);
    }
    75% {
        transform: rotate(-3deg);
    }
}

@keyframes leavesRustle {
    0%, 100% {
        transform: scale(1) rotate(0deg);
    }
    50% {
        transform: scale(1.05) rotate(2deg);
    }
}

/* 页脚 */
.footer {
    text-align: center;
    color: #666;
    font-size: 0.9rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container { padding: 1rem; }
    .title { font-size: 2rem; }
    .weather-cards { grid-template-columns: 1fr; }
    .controls { gap: 0.5rem; }
    .control-btn { padding: 0.8rem 1.5rem; font-size: 0.9rem; }
}
