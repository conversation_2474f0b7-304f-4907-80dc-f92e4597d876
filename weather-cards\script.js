// 天气卡片动画控制器
class WeatherCardController {
    constructor() {
        this.currentWeather = 'sunny';
        this.cards = document.querySelectorAll('.weather-card');
        this.buttons = document.querySelectorAll('.control-btn');
        this.animationTimers = new Map();
        
        this.init();
    }

    init() {
        // 绑定按钮事件
        this.buttons.forEach(button => {
            button.addEventListener('click', (e) => {
                const weatherType = e.currentTarget.dataset.weather;
                this.switchWeather(weatherType);
            });
        });

        // 初始化第一个卡片
        this.showWeatherCard('sunny');
        
        // 添加键盘快捷键
        document.addEventListener('keydown', (e) => {
            this.handleKeyboard(e);
        });

        // 添加自动轮播功能（可选）
        this.startAutoRotation();
    }

    switchWeather(weatherType) {
        if (this.currentWeather === weatherType) return;
        
        // 更新按钮状态
        this.updateButtonStates(weatherType);
        
        // 切换卡片显示
        this.showWeatherCard(weatherType);
        
        // 播放切换音效（如果需要）
        this.playTransitionSound();
        
        this.currentWeather = weatherType;
    }

    updateButtonStates(activeWeather) {
        this.buttons.forEach(button => {
            button.classList.remove('active');
            if (button.dataset.weather === activeWeather) {
                button.classList.add('active');
            }
        });
    }

    showWeatherCard(weatherType) {
        // 隐藏所有卡片
        this.cards.forEach(card => {
            card.classList.remove('active');
        });

        // 显示目标卡片
        const targetCard = document.getElementById(`${weatherType}-card`);
        if (targetCard) {
            // 添加延迟以创建平滑过渡效果
            setTimeout(() => {
                targetCard.classList.add('active');
                this.triggerSpecialEffects(weatherType);
            }, 100);
        }
    }

    triggerSpecialEffects(weatherType) {
        // 清除之前的特效定时器
        this.clearAnimationTimers();

        switch (weatherType) {
            case 'sunny':
                this.createSunnyEffects();
                break;
            case 'rainy':
                this.createRainyEffects();
                break;
            case 'snowy':
                this.createSnowyEffects();
                break;
            case 'windy':
                this.createWindyEffects();
                break;
        }
    }

    createSunnyEffects() {
        const card = document.getElementById('sunny-card');
        const sunRays = card.querySelector('.sun-rays');
        
        // 动态添加更多光线效果
        const timer = setInterval(() => {
            this.createSparkle(card);
        }, 2000);
        
        this.animationTimers.set('sunny', timer);
    }

    createRainyEffects() {
        const card = document.getElementById('rainy-card');
        
        // 动态添加雨滴
        const timer = setInterval(() => {
            this.createRainDrop(card);
        }, 200);
        
        this.animationTimers.set('rainy', timer);
    }

    createSnowyEffects() {
        const card = document.getElementById('snowy-card');
        
        // 动态添加雪花
        const timer = setInterval(() => {
            this.createSnowflake(card);
        }, 500);
        
        this.animationTimers.set('snowy', timer);
    }

    createWindyEffects() {
        const card = document.getElementById('windy-card');
        
        // 动态添加风线
        const timer = setInterval(() => {
            this.createWindLine(card);
        }, 800);
        
        this.animationTimers.set('windy', timer);
    }

    createSparkle(card) {
        const sparkle = document.createElement('div');
        sparkle.className = 'sparkle';
        sparkle.style.cssText = `
            position: absolute;
            width: 4px;
            height: 4px;
            background: #fff;
            border-radius: 50%;
            top: ${Math.random() * 60 + 20}%;
            left: ${Math.random() * 60 + 20}%;
            animation: sparkleAnimation 1.5s ease-out forwards;
            pointer-events: none;
            z-index: 5;
        `;
        
        card.appendChild(sparkle);
        
        setTimeout(() => {
            if (sparkle.parentNode) {
                sparkle.parentNode.removeChild(sparkle);
            }
        }, 1500);
    }

    createRainDrop(card) {
        const rainContainer = card.querySelector('.rain-container');
        const drop = document.createElement('div');
        drop.className = 'dynamic-rain-drop';
        drop.style.cssText = `
            position: absolute;
            width: 2px;
            height: 15px;
            background: linear-gradient(to bottom, transparent, #74b9ff, #0984e3);
            border-radius: 0 0 50% 50%;
            left: ${Math.random() * 100}%;
            top: -20px;
            animation: rainFall 1s linear forwards;
            pointer-events: none;
        `;
        
        rainContainer.appendChild(drop);
        
        setTimeout(() => {
            if (drop.parentNode) {
                drop.parentNode.removeChild(drop);
            }
        }, 1000);
    }

    createSnowflake(card) {
        const snowContainer = card.querySelector('.snow-container');
        const flake = document.createElement('div');
        const symbols = ['❄', '❅', '❆'];
        flake.textContent = symbols[Math.floor(Math.random() * symbols.length)];
        flake.className = 'dynamic-snowflake';
        flake.style.cssText = `
            position: absolute;
            color: #ffffff;
            font-size: ${Math.random() * 0.8 + 0.8}rem;
            left: ${Math.random() * 100}%;
            top: -20px;
            animation: snowFall ${Math.random() * 2 + 2}s linear forwards;
            pointer-events: none;
            opacity: 0.8;
        `;
        
        snowContainer.appendChild(flake);
        
        setTimeout(() => {
            if (flake.parentNode) {
                flake.parentNode.removeChild(flake);
            }
        }, 4000);
    }

    createWindLine(card) {
        const windContainer = card.querySelector('.wind-lines');
        const line = document.createElement('div');
        line.className = 'dynamic-wind-line';
        line.style.cssText = `
            position: absolute;
            height: 2px;
            width: ${Math.random() * 40 + 40}px;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.6), transparent);
            border-radius: 2px;
            top: ${Math.random() * 60 + 20}%;
            left: -100px;
            animation: windFlow 2s ease-in-out forwards;
            pointer-events: none;
        `;
        
        windContainer.appendChild(line);
        
        setTimeout(() => {
            if (line.parentNode) {
                line.parentNode.removeChild(line);
            }
        }, 2000);
    }

    clearAnimationTimers() {
        this.animationTimers.forEach((timer, key) => {
            clearInterval(timer);
        });
        this.animationTimers.clear();
    }

    handleKeyboard(e) {
        const keyMap = {
            '1': 'sunny',
            '2': 'rainy',
            '3': 'snowy',
            '4': 'windy'
        };
        
        if (keyMap[e.key]) {
            this.switchWeather(keyMap[e.key]);
        }
    }

    playTransitionSound() {
        // 创建简单的音效反馈
        if ('AudioContext' in window || 'webkitAudioContext' in window) {
            try {
                const audioContext = new (window.AudioContext || window.webkitAudioContext)();
                const oscillator = audioContext.createOscillator();
                const gainNode = audioContext.createGain();
                
                oscillator.connect(gainNode);
                gainNode.connect(audioContext.destination);
                
                oscillator.frequency.setValueAtTime(800, audioContext.currentTime);
                oscillator.frequency.exponentialRampToValueAtTime(400, audioContext.currentTime + 0.1);
                
                gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
                gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.1);
                
                oscillator.start(audioContext.currentTime);
                oscillator.stop(audioContext.currentTime + 0.1);
            } catch (e) {
                // 静默处理音频错误
            }
        }
    }

    startAutoRotation() {
        // 可选的自动轮播功能
        const weatherTypes = ['sunny', 'rainy', 'snowy', 'windy'];
        let currentIndex = 0;
        
        // 每10秒自动切换（可以通过设置禁用）
        setInterval(() => {
            // 只有在用户没有手动操作时才自动切换
            if (document.hidden) return; // 页面不可见时不切换
            
            currentIndex = (currentIndex + 1) % weatherTypes.length;
            // this.switchWeather(weatherTypes[currentIndex]); // 取消注释以启用自动轮播
        }, 10000);
    }
}

// 添加额外的CSS动画
const additionalStyles = `
    @keyframes sparkleAnimation {
        0% { opacity: 0; transform: scale(0); }
        50% { opacity: 1; transform: scale(1); }
        100% { opacity: 0; transform: scale(0); }
    }
    
    .sparkle {
        box-shadow: 0 0 10px #fff;
    }
`;

// 注入额外样式
const styleSheet = document.createElement('style');
styleSheet.textContent = additionalStyles;
document.head.appendChild(styleSheet);

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    new WeatherCardController();
    
    // 添加页面加载动画
    document.body.style.opacity = '0';
    document.body.style.transition = 'opacity 0.5s ease-in-out';
    
    setTimeout(() => {
        document.body.style.opacity = '1';
    }, 100);
});

// 添加页面可见性变化处理
document.addEventListener('visibilitychange', () => {
    if (document.hidden) {
        // 页面隐藏时暂停动画
        document.body.style.animationPlayState = 'paused';
    } else {
        // 页面显示时恢复动画
        document.body.style.animationPlayState = 'running';
    }
});
