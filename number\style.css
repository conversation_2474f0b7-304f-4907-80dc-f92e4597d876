:root {
    --primary-color: #4a63e7;
    --secondary-color: #6c7ae0;
    --accent-color: #ff6b6b;
    --background-color: #f5f7ff;
    --text-color: #333;
    --card-background: #ffffff;
    --success-color: #28a745;
    --warning-color: #ffc107;
    --error-color: #dc3545;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Poppins', sans-serif;
    background-color: var(--background-color);
    color: var(--text-color);
    min-height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 20px;
    background-image: radial-gradient(circle at 10% 20%, rgb(239, 246, 249) 0%, rgb(206, 239, 253) 90%);
}

.container {
    width: 100%;
    max-width: 700px;
}

.game-box {
    background-color: var(--card-background);
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 10px 30px rgba(74, 99, 231, 0.1);
    margin-bottom: 20px;
    animation: fadeIn 0.5s ease-in-out;
    position: relative;
    overflow: hidden;
}

.game-box::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 5px;
    background: linear-gradient(to right, var(--primary-color), var(--secondary-color));
}

.title {
    text-align: center;
    margin-bottom: 20px;
    color: var(--primary-color);
    font-size: 2.5rem;
    font-weight: 700;
    letter-spacing: 1px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
}

.game-info {
    margin-bottom: 25px;
    text-align: center;
}

.game-info p {
    margin-bottom: 10px;
    font-size: 1.1rem;
}

.attempts {
    font-weight: 600;
    color: var(--primary-color);
}

.progress-container {
    width: 100%;
    height: 10px;
    background-color: #e0e0e0;
    border-radius: 5px;
    margin-top: 15px;
    overflow: hidden;
}

.progress-bar {
    height: 100%;
    width: 100%;
    background: linear-gradient(to right, var(--primary-color), var(--secondary-color));
    border-radius: 5px;
    transition: width 0.3s ease-in-out;
}

.input-section {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
}

input {
    flex: 1;
    padding: 15px;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    font-size: 1rem;
    font-family: inherit;
    transition: border-color 0.3s;
}

input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(74, 99, 231, 0.2);
}

button {
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: 8px;
    padding: 15px 20px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s;
    font-family: inherit;
}

button:hover {
    background-color: var(--secondary-color);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(74, 99, 231, 0.3);
}

button:active {
    transform: translateY(0);
    box-shadow: 0 2px 5px rgba(74, 99, 231, 0.3);
}

.message-box {
    background-color: #f0f4ff;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 20px;
    text-align: center;
    min-height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
}

#message {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--primary-color);
}

.message-success {
    background-color: rgba(40, 167, 69, 0.1);
    color: var(--success-color) !important;
}

.message-warning {
    background-color: rgba(255, 193, 7, 0.1);
    color: var(--warning-color) !important;
}

.message-error {
    background-color: rgba(220, 53, 69, 0.1);
    color: var(--error-color) !important;
}

.history {
    background-color: #f8faff;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 20px;
}

.history h3 {
    margin-bottom: 10px;
    color: var(--primary-color);
}

#history-list {
    list-style-type: none;
    max-height: 150px;
    overflow-y: auto;
}

#history-list li {
    padding: 8px 10px;
    border-bottom: 1px solid #e0e0e0;
    display: flex;
    justify-content: space-between;
}

#history-list li:last-child {
    border-bottom: none;
}

.too-high {
    color: var(--error-color);
}

.too-low {
    color: var(--warning-color);
}

.correct {
    color: var(--success-color);
}

.control-btns {
    display: flex;
    justify-content: center;
    gap: 10px;
}

.back-btn {
    display: inline-block;
    background-color: #28a745;
    color: white;
    text-decoration: none;
    border-radius: 8px;
    padding: 15px 20px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s;
    font-family: inherit;
}

.back-btn:hover {
    background-color: #219a3a;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(40, 167, 69, 0.3);
}

.back-btn:active {
    transform: translateY(0);
    box-shadow: 0 2px 5px rgba(40, 167, 69, 0.3);
}

.hidden {
    display: none;
}

.stats {
    display: flex;
    gap: 20px;
}

.stat-box {
    flex: 1;
    background-color: var(--card-background);
    border-radius: 15px;
    padding: 20px;
    text-align: center;
    box-shadow: 0 5px 15px rgba(74, 99, 231, 0.1);
    transition: transform 0.3s;
}

.stat-box:hover {
    transform: translateY(-5px);
}

.stat-title {
    font-size: 1rem;
    color: var(--secondary-color);
    margin-bottom: 5px;
}

.stat-value {
    font-size: 2rem;
    font-weight: 700;
    color: var(--primary-color);
}

.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    justify-content: center;
    align-items: center;
}

.modal-content {
    background-color: var(--card-background);
    padding: 30px;
    border-radius: 15px;
    max-width: 450px;
    width: 90%;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    animation: slideIn 0.3s ease-out;
}

#modal-title {
    color: var(--primary-color);
    margin-bottom: 15px;
}

#modal-message {
    margin-bottom: 20px;
    font-size: 1.1rem;
}

#modal-close {
    background-color: var(--accent-color);
}

#modal-close:hover {
    background-color: #ff5252;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(-50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 响应式设计 */
@media (max-width: 600px) {
    .title {
        font-size: 2rem;
    }
    
    .input-section {
        flex-direction: column;
    }
    
    .stats {
        flex-direction: column;
        gap: 10px;
    }
}