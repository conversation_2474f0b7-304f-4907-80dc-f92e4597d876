# Product Context

This file provides a high-level overview of the project and the expected product that will be created. Initially it is based upon projectBrief.md (if provided) and all other available project-related information in the working directory. This file is intended to be updated as the project evolves, and should be used to inform all other modes of the project's goals and context.
2025-04-13 12:47:51 - Log of updates made will be appended as footnotes to the end of this file.

## Project Goal

The project is a collection of web applications demonstrating various web development techniques and showcasing the Roo Code Memory Bank system application scenarios.

## Key Features

- Weather application (Chinese cities)
- Snake game (Canvas implementation)
- Number guessing game (1-100)
- Bingo game (standard 5x5 card)
- Personal resume website

## Overall Architecture

Frontend: HTML5, CSS3, JavaScript
Games: Canvas API
Tools: VS Code integration