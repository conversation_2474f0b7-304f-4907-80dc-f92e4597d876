<!DOCTYPE html>
<html>
<head>
    <title>贪吃蛇游戏</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="game-container">
        <h1>贪吃蛇游戏</h1>
        <div id="game-info">
            <div id="score">分数: 0</div>
            <div id="status">状态: 准备开始</div>
        </div>
        <canvas id="gameCanvas" width="400" height="400"></canvas>
        <div id="controls">
            <button id="startBtn">开始游戏</button>
            <div class="instructions">
                <p>使用方向键(↑↓←→)控制蛇的移动</p>
                <p>吃到红色食物增加分数</p>
                <p>撞墙或撞到自己游戏结束</p>
            </div>
        </div>
        <div id="gameOver" class="hidden">
            <h2>游戏结束!</h2>
            <p>最终分数: <span id="finalScore">0</span></p>
            <button id="restartBtn">重新开始</button>
        </div>
        <div class="back-home">
            <a href="../index.html" class="back-btn">返回主菜单</a>
        </div>
    </div>
    <script src="script.js"></script>
</body>
</html>