// DOM元素
const gameContainer = document.getElementById('game-container');
const startBtn = document.getElementById('start-btn');
const resetBtn = document.getElementById('reset-btn');
const flipsCounter = document.getElementById('flips');
const timeCounter = document.getElementById('time');
const gameOverScreen = document.getElementById('game-over');
const finalFlips = document.getElementById('final-flips');
const finalTime = document.getElementById('final-time');
const playAgainBtn = document.getElementById('play-again');
const difficultyBtns = document.querySelectorAll('.difficulty-btn');

// 游戏变量
let cards = [];
let hasFlippedCard = false;
let lockBoard = false;
let firstCard, secondCard;
let flips = 0;
let matchedPairs = 0;
let totalPairs = 0;
let timer;
let seconds = 0;
let minutes = 0;
let gameStarted = false;
let currentGrid = '4x3'; // 默认4x3网格 (简单难度)

// 卡片图案（使用Font Awesome图标）
const cardIcons = [
    'fa-heart', 'fa-star', 'fa-smile', 'fa-moon', 
    'fa-sun', 'fa-cloud', 'fa-bell', 'fa-apple-whole',
    'fa-car', 'fa-plane', 'fa-bicycle', 'fa-rocket',
    'fa-fish', 'fa-cat', 'fa-dog', 'fa-dragon',
    'fa-gamepad', 'fa-ghost', 'fa-key', 'fa-lemon'
];

// 初始化游戏
function initGame() {
    clearGame();
    let rows, cols;
    [cols, rows] = currentGrid.split('x').map(num => parseInt(num));
    totalPairs = (rows * cols) / 2;
    
    // 根据网格尺寸设置样式
    gameContainer.style.gridTemplateColumns = `repeat(${cols}, 1fr)`;
    
    // 选择适量的图标
    const selectedIcons = cardIcons.slice(0, totalPairs);
    
    // 创建卡片对（每个图标两张卡片）
    let cardPairs = [];
    selectedIcons.forEach(icon => {
        cardPairs.push(
            createCard(icon),
            createCard(icon)
        );
    });
    
    // 洗牌并添加到游戏容器
    shuffleArray(cardPairs);
    cardPairs.forEach(card => {
        gameContainer.appendChild(card);
        cards.push(card);
    });
}

// 创建卡片元素
function createCard(icon) {
    const card = document.createElement('div');
    card.classList.add('card');
    
    const cardFront = document.createElement('div');
    cardFront.classList.add('card-front');
    cardFront.innerHTML = `<i class="fas ${icon}"></i>`;
    
    const cardBack = document.createElement('div');
    cardBack.classList.add('card-back');
    
    card.appendChild(cardFront);
    card.appendChild(cardBack);
    
    card.addEventListener('click', flipCard);
    
    return card;
}

// 洗牌算法 (Fisher-Yates)
function shuffleArray(array) {
    for (let i = array.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [array[i], array[j]] = [array[j], array[i]];
    }
    return array;
}

// 翻牌函数
function flipCard() {
    if (lockBoard) return;
    if (this === firstCard) return;
    
    // 如果是第一次点击卡片，启动计时器
    if (!gameStarted) {
        startTimer();
        gameStarted = true;
    }
    
    this.classList.add('flipped');
    
    if (!hasFlippedCard) {
        // 第一张卡片
        hasFlippedCard = true;
        firstCard = this;
        return;
    }
    
    // 第二张卡片
    secondCard = this;
    flips++;
    flipsCounter.textContent = flips;
    
    checkForMatch();
}

// 检查两张卡片是否匹配
function checkForMatch() {
    const isMatch = firstCard.querySelector('.card-front i').className === 
                    secondCard.querySelector('.card-front i').className;
    
    isMatch ? disableCards() : unflipCards();
}

// 匹配成功，禁用卡片
function disableCards() {
    firstCard.removeEventListener('click', flipCard);
    secondCard.removeEventListener('click', flipCard);
    
    matchedPairs++;
    if (matchedPairs === totalPairs) {
        setTimeout(() => {
            endGame();
        }, 1000);
    }
    
    resetBoard();
}

// 匹配失败，翻回卡片
function unflipCards() {
    lockBoard = true;
    
    setTimeout(() => {
        firstCard.classList.remove('flipped');
        secondCard.classList.remove('flipped');
        
        resetBoard();
    }, 1000);
}

// 重置临时变量
function resetBoard() {
    [hasFlippedCard, lockBoard] = [false, false];
    [firstCard, secondCard] = [null, null];
}

// 清除游戏
function clearGame() {
    gameContainer.innerHTML = '';
    cards = [];
    matchedPairs = 0;
    flips = 0;
    seconds = 0;
    minutes = 0;
    gameStarted = false;
    flipsCounter.textContent = '0';
    timeCounter.textContent = '00:00';
    
    if (timer) {
        clearInterval(timer);
        timer = null;
    }
    
    resetBoard();
}

// 开始计时器
function startTimer() {
    timer = setInterval(() => {
        seconds++;
        if (seconds === 60) {
            minutes++;
            seconds = 0;
        }
        
        timeCounter.textContent = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    }, 1000);
}

// 游戏结束
function endGame() {
    clearInterval(timer);
    
    finalFlips.textContent = flips;
    finalTime.textContent = timeCounter.textContent;
    
    gameOverScreen.classList.remove('hidden');
}

// 切换难度
function changeDifficulty() {
    difficultyBtns.forEach(btn => {
        btn.classList.remove('active');
    });
    
    this.classList.add('active');
    currentGrid = this.dataset.grid;
    
    if (gameStarted) {
        resetGame();
    } else {
        initGame();
    }
}

// 重置游戏
function resetGame() {
    clearGame();
    initGame();
}

// 事件监听器
startBtn.addEventListener('click', () => {
    if (!gameStarted) {
        gameStarted = true;
        startTimer();
        startBtn.textContent = '游戏中...';
        startBtn.disabled = true;
    }
});
resetBtn.addEventListener('click', resetGame);
playAgainBtn.addEventListener('click', () => {
    gameOverScreen.classList.add('hidden');
    resetGame();
});
difficultyBtns.forEach(btn => {
    btn.addEventListener('click', changeDifficulty);
});

// 初始化游戏
window.addEventListener('load', initGame); 