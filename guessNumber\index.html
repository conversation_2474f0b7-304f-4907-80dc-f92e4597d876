<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>猜数字游戏</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="container">
        <h1>猜数字游戏</h1>
        <p class="game-description">我已经想好了一个1到100之间的数字，你能猜出来吗？</p>
        
        <div class="game-area">
            <input type="number" id="guess-input" min="1" max="100" placeholder="输入你猜的数字">
            <button id="guess-button">猜！</button>
            <button id="reset-button">重新开始</button>
        </div>
        
        <div class="message-area">
            <p id="message">请输入1到100之间的数字</p>
            <p id="history-title" class="hidden">你的猜测历史：</p>
            <ul id="guess-history"></ul>
        </div>
        
        <div class="stats-area hidden" id="stats-area">
            <h2>游戏统计</h2>
            <p>猜测次数：<span id="guess-count">0</span></p>
            <p>用时：<span id="time-used">0</span>秒</p>
        </div>
        
        <div class="back-to-home">
            <a href="../index.html" class="back-btn">返回主菜单</a>
        </div>
    </div>
    
    <script src="script.js"></script>
</body>
</html>