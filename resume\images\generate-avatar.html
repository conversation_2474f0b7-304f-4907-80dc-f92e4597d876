<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>生成默认头像</title>
    <style>
        body {
            font-family: 'Noto Sans SC', sans-serif;
            text-align: center;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        }
        canvas {
            border: 1px solid #ddd;
            margin: 20px 0;
            border-radius: 50%;
        }
        button {
            background-color: #1e88e5;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 5px;
        }
        button:hover {
            background-color: #1565c0;
        }
        h1 {
            color: #1e88e5;
        }
        p {
            margin-bottom: 20px;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>默认头像生成器</h1>
        <p>这个工具可以帮助您生成简单的默认头像。点击"生成头像"按钮创建一个随机头像，然后点击"保存头像"下载图像。</p>
        
        <canvas id="avatarCanvas" width="200" height="200"></canvas>
        <div>
            <button id="generateBtn">生成头像</button>
            <button id="saveBtn">保存头像</button>
        </div>
        <p>保存后，将图像命名为 "profile.jpg" 或 "default-avatar.png" 并放在 images 文件夹中</p>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const canvas = document.getElementById('avatarCanvas');
            const ctx = canvas.getContext('2d');
            const generateBtn = document.getElementById('generateBtn');
            const saveBtn = document.getElementById('saveBtn');
            
            // 生成随机颜色
            function getRandomColor() {
                const letters = '0123456789ABCDEF';
                let color = '#';
                for (let i = 0; i < 6; i++) {
                    color += letters[Math.floor(Math.random() * 16)];
                }
                return color;
            }
            
            // 生成头像
            function generateAvatar() {
                const size = canvas.width;
                const bgColor = getRandomColor();
                const fgColor = '#FFFFFF';
                
                // 清空画布
                ctx.clearRect(0, 0, size, size);
                
                // 绘制圆形背景
                ctx.fillStyle = bgColor;
                ctx.beginPath();
                ctx.arc(size/2, size/2, size/2, 0, Math.PI * 2);
                ctx.fill();
                
                // 绘制简单的头部轮廓
                ctx.fillStyle = fgColor;
                ctx.beginPath();
                ctx.arc(size/2, size/2 - 15, size/4, 0, Math.PI * 2);
                ctx.fill();
                
                // 绘制身体轮廓
                ctx.beginPath();
                ctx.arc(size/2, size/2 + 50, size/3, 0, Math.PI);
                ctx.fill();
            }
            
            // 保存头像
            function saveAvatar() {
                const link = document.createElement('a');
                link.download = 'default-avatar.png';
                link.href = canvas.toDataURL('image/png');
                link.click();
            }
            
            // 事件监听
            generateBtn.addEventListener('click', generateAvatar);
            saveBtn.addEventListener('click', saveAvatar);
            
            // 初始生成一个头像
            generateAvatar();
        });
    </script>
</body>
</html>